{"name": "@workflow-mapper/api", "version": "0.0.1", "type": "module", "scripts": {"dev": "nodemon src/index.ts", "build": "tsup src/index.ts --format esm", "start": "node dist/index.js", "test": "jest", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"express": "4.19.2"}, "devDependencies": {"@types/express": "4.17.21", "@types/node": "20.12.7", "typescript": "5.4.3", "tsup": "8.0.2", "nodemon": "3.1.0", "jest": "29.7.0", "@types/jest": "29.5.12", "ts-jest": "29.1.2", "supertest": "7.1.1", "@types/supertest": "6.0.2"}}