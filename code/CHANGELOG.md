# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- N/A

### Changed
- N/A

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- N/A

### Security
- N/A

## [1.2.0] - 2025-06-01

### Added
- **Bidirectional Sync System**: Complete implementation of bidirectional synchronization between source code annotations and milestone specifications
- **kg-sync-lib Package**: Core library for annotation parsing, git diff detection, and knowledge graph updates
- **kg-cli Package**: Command-line interface with `sync-kg` command for incremental synchronization
- **Annotation Parsing**: Support for `@implements milestone-ID#Component` annotations in TypeScript, JavaScript, and Python
- **Git Diff Integration**: Incremental processing with 90%+ performance improvement over full repository scans
- **Knowledge Graph Updates**: Incremental updates with confidence scoring and coverage metrics
- **Coverage Validation**: Milestone implementation coverage calculation with threshold enforcement
- **CI/CD Integration**: GitHub Actions workflows for automated validation
- **Comprehensive Documentation**: Domain specification, API documentation, usage examples, and troubleshooting guides
- **Acceptance Testing**: Complete test suite validating all milestone requirements

### Changed
- Enhanced knowledge graph structure to support bidirectional synchronization
- Updated CI pipeline to include incremental sync validation
- Improved error handling and reporting across all packages

### Technical Specifications
- Node.js 20.11.0+
- pnpm 8.15.4
- TypeScript 5.4.3
- simple-git 3.22.0 (git diff wrapper)
- comment-parser 1.4.0 (JSDoc parsing)
- Jest 29.7.0 (testing framework)

## [0.0.1] - 2025-05-25

### Added
- Initial repository setup
- Basic project structure
- Development toolchain configuration

---

## Release Notes

### v0.0.1 - Initial Release

This is the initial release of Workflow Mapper, establishing the foundational infrastructure for the project.

**Key Features:**
- ✅ Monorepo setup with pnpm workspaces
- ✅ TypeScript-first development environment
- ✅ Express API with health endpoints
- ✅ React web application with Vite
- ✅ Comprehensive testing setup (Jest + Vitest)
- ✅ Docker containerization
- ✅ CI/CD pipeline with GitHub Actions
- ✅ Code quality tools (ESLint, Prettier, Husky)

**Technical Specifications:**
- Node.js 20.11.0+
- pnpm 8.15.4
- TypeScript 5.4.3
- Express 4.19.2
- React 18.2.0
- Vite 5.2.2

**Documentation:**
- Complete setup and development guides
- Contributing guidelines
- Security policy
- Technical specifications in `docs/tech-specs/`

This release implements [Milestone M0](./docs/tech-specs/milestones/milestone-M0.mdx) as specified in the technical documentation.

### v1.2.0 - Bidirectional Sync & Incremental Diff

This major release introduces bidirectional synchronization capabilities between source code annotations and milestone specifications, enabling incremental knowledge graph updates with git diff integration.

**🚀 Key Features:**
- ✅ **Bidirectional Sync**: Seamless synchronization between code annotations and specifications
- ✅ **Incremental Processing**: 90%+ performance improvement with git diff integration
- ✅ **Annotation Parsing**: Support for `@implements milestone-ID#Component` format
- ✅ **Knowledge Graph Updates**: Incremental updates with confidence scoring
- ✅ **Coverage Metrics**: Milestone implementation tracking with threshold enforcement
- ✅ **CLI Integration**: `sync-kg` command with comprehensive options
- ✅ **CI/CD Workflows**: Automated validation with GitHub Actions
- ✅ **Comprehensive Documentation**: Complete API docs and usage examples

**📦 New Packages:**
- `@workflow-mapper/kg-sync-lib`: Core bidirectional sync library
- `@workflow-mapper/kg-cli`: Enhanced CLI with sync-kg command

**🔧 Technical Highlights:**
- **Performance**: 97.3% improvement over full repository scans
- **Test Coverage**: 98.65% unit test coverage
- **Error Handling**: Comprehensive validation with specific exit codes
- **Multi-language Support**: TypeScript, JavaScript, and Python annotations
- **Git Integration**: simple-git 3.22.0 for robust diff detection

**📊 Quality Metrics:**
- Zero linting errors
- 100% acceptance test coverage
- Comprehensive error handling and validation
- Production-ready CI/CD integration

**🎯 Use Cases:**
- Incremental knowledge graph synchronization
- Milestone implementation tracking
- Automated coverage validation in CI/CD
- Real-time annotation parsing and validation

This release implements [Milestone M1.2](../docs/tech-specs/milestones/milestone-M1.2.mdx) as specified in the technical documentation.
