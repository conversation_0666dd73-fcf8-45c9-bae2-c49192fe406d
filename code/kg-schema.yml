# Knowledge Graph Schema for Workflow Mapper
# Defines entities, relationships, and properties for the knowledge graph

version: "1.0.0"
namespace: "https://workflow-mapper.dev/vocab#"

# Entity Types
entities:
  Specification:
    description: "Base type for all specification documents"
    properties:
      - id: "Unique identifier for the specification"
      - title: "Human-readable title"
      - description: "Brief description of the specification"
      - status: "Current status (Draft, Active, Deprecated, etc.)"
      - version: "Version number"
      - created: "Creation date"
      - updated: "Last update date"
      - tags: "List of tags for categorization"
      - authors: "List of authors"
      - filePath: "Path to the source file"

  Milestone:
    extends: Specification
    description: "Project milestone specification"
    properties:
      - deliverables: "List of deliverables"
      - successCriteria: "Success criteria for completion"
      - taskBreakdown: "Breakdown of tasks"
      - dependencies: "Dependencies on other milestones"

  Component:
    extends: Specification
    description: "Software component specification"
    properties:
      - interfaces: "Component interfaces"
      - dependencies: "Component dependencies"
      - implementation: "Implementation details"

  Domain:
    extends: Specification
    description: "Domain or business area specification"
    properties:
      - scope: "Domain scope and boundaries"
      - stakeholders: "Key stakeholders"
      - processes: "Business processes"

  ArchitecturalDecision:
    extends: Specification
    description: "Architectural Decision Record (ADR)"
    properties:
      - context: "Decision context"
      - decision: "The decision made"
      - consequences: "Consequences of the decision"
      - alternatives: "Alternative options considered"

# Relationship Types
relationships:
  implements:
    description: "Entity implements another entity"
    domain: [Specification]
    range: [Specification]
    properties:
      - confidence: "Confidence level of the relationship"
      - notes: "Additional notes about the implementation"

  dependsOn:
    description: "Entity depends on another entity"
    domain: [Specification]
    range: [Specification]
    properties:
      - type: "Type of dependency (hard, soft, optional)"
      - reason: "Reason for the dependency"

  contains:
    description: "Entity contains another entity"
    domain: [Specification]
    range: [Specification]
    properties:
      - relationship: "Nature of containment"

  supersedes:
    description: "Entity supersedes another entity"
    domain: [Specification]
    range: [Specification]
    properties:
      - effectiveDate: "When the supersession takes effect"

  references:
    description: "Entity references another entity"
    domain: [Specification]
    range: [Specification]
    properties:
      - context: "Context of the reference"

# Status Values
statusValues:
  - Draft: "Initial draft, work in progress"
  - Review: "Under review"
  - Active: "Approved and active"
  - Implemented: "Successfully implemented"
  - Deprecated: "No longer recommended"
  - Archived: "Archived for historical reference"

# Confidence Levels
confidenceLevels:
  - High: "High confidence (90-100%)"
  - Medium: "Medium confidence (70-89%)"
  - Low: "Low confidence (50-69%)"
  - Unknown: "Confidence level unknown"

# Validation Rules
validation:
  required:
    - id: "Every entity must have a unique ID"
    - title: "Every entity must have a title"
    - filePath: "Every entity must reference a source file"
  
  constraints:
    - "Entity IDs must be unique across the entire graph"
    - "File paths must be valid and accessible"
    - "Status values must be from the defined list"
    - "Relationships must reference existing entities"

# JSON-LD Context
jsonldContext:
  "@vocab": "https://workflow-mapper.dev/vocab#"
  title: "https://schema.org/name"
  description: "https://schema.org/description"
  status: "https://workflow-mapper.dev/vocab#status"
  version: "https://schema.org/version"
  created: "https://schema.org/dateCreated"
  updated: "https://schema.org/dateModified"
  tags: "https://schema.org/keywords"
  authors: "https://schema.org/author"
  filePath: "https://workflow-mapper.dev/vocab#filePath"
  implements: "https://workflow-mapper.dev/vocab#implements"
  dependsOn: "https://workflow-mapper.dev/vocab#dependsOn"
  contains: "https://workflow-mapper.dev/vocab#contains"
  supersedes: "https://workflow-mapper.dev/vocab#supersedes"
  references: "https://workflow-mapper.dev/vocab#references"
