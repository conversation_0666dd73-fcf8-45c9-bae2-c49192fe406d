# Contributing to Workflow Mapper

Thank you for your interest in contributing to Workflow Mapper! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 20.11.0+
- pnpm 8.15.4+
- Docker & Docker Compose
- Git

### Development Setup

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/your-username/workflow-mapper.git
   cd workflow-mapper
   ```
3. Install dependencies:
   ```bash
   pnpm install
   ```
4. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

## 🛠 Development Workflow

### Code Style

- We use ESL<PERSON> and Prettier for code formatting
- Pre-commit hooks automatically format code
- TypeScript strict mode is enforced

### Testing

- Write tests for new features
- Ensure all tests pass: `pnpm test`
- Maintain test coverage

### Building

- Build all packages: `pnpm build`
- Type check: `pnpm type-check`
- Lint: `pnpm lint`

## 📝 Commit Guidelines

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

### Examples

```
feat(api): add health endpoint
fix(web): resolve proxy configuration issue
docs: update contributing guidelines
```

## 🔍 Pull Request Process

1. Ensure your code follows the style guidelines
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure all CI checks pass
5. Request review from maintainers

### PR Checklist

- [ ] Code follows style guidelines
- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] CI checks pass
- [ ] No breaking changes (or properly documented)

## 🏗 Architecture Guidelines

### Monorepo Structure

- `apps/api`: Express API server
- `apps/web`: React web application
- `packages/shared`: Shared utilities and types

### Code Organization

- Use named exports only
- Keep functions under 60 lines
- Use TypeScript strict mode
- Follow the Result<T, E> pattern for error handling

### Dependencies

- Add dependencies to the appropriate workspace
- Use exact versions for critical dependencies
- Document any new dependencies

## 🐛 Bug Reports

When filing bug reports, please include:

- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version, etc.)
- Relevant logs or error messages

## 💡 Feature Requests

For feature requests, please:

- Check existing issues first
- Provide clear use case
- Explain the problem it solves
- Consider implementation complexity

## 📞 Getting Help

- Check the documentation first
- Search existing issues
- Ask questions in discussions
- Contact maintainers for urgent issues

## 🙏 Recognition

Contributors will be recognized in:

- CHANGELOG.md for significant contributions
- README.md contributors section
- Release notes for major features

Thank you for contributing to Workflow Mapper!
