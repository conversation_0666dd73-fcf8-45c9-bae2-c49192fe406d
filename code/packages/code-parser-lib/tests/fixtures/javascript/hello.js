/**
 * Simple JavaScript test file for code parser testing.
 * Contains multiple functions and function calls.
 */

function add(a, b) {
    return a + b;
}

function multiply(x, y) {
    return x * y;
}

function calculateArea(length, width) {
    return multiply(length, width);
}

const greet = (name) => {
    return `Hello, ${name}!`;
};

function processData(data) {
    const processed = data.map(item => multiply(item, 2));
    return processed;
}

class Calculator {
    constructor() {
        this.history = [];
    }
    
    addNumbers(a, b) {
        const result = add(a, b);
        this.history.push({ operation: 'add', result });
        return result;
    }
    
    multiplyNumbers(x, y) {
        const result = multiply(x, y);
        this.history.push({ operation: 'multiply', result });
        return result;
    }
}

function main() {
    // Test basic arithmetic
    const result1 = add(5, 3);
    const result2 = multiply(4, 6);
    
    // Test area calculation
    const area = calculateArea(10, 20);
    
    // Test greeting
    const message = greet("World");
    
    // Test data processing
    const data = [1, 2, 3, 4, 5];
    const processed = processData(data);
    
    // Test calculator class
    const calc = new Calculator();
    const sum = calc.addNumbers(10, 15);
    const product = calc.multiplyNumbers(3, 7);
    
    console.log(`Addition result: ${result1}`);
    console.log(`Multiplication result: ${result2}`);
    console.log(`Area: ${area}`);
    console.log(message);
    console.log(`Processed data: ${processed}`);
    console.log(`Calculator sum: ${sum}`);
    console.log(`Calculator product: ${product}`);
    
    return true;
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        add,
        multiply,
        calculateArea,
        greet,
        processData,
        Calculator,
        main
    };
}

// Run main if this is the entry point
if (typeof require !== 'undefined' && require.main === module) {
    main();
}
