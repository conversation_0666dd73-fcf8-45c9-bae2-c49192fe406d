#!/usr/bin/env python3
"""
Simple Python test file for code parser testing.
Contains multiple functions and function calls.
"""

def add(a, b):
    """Add two numbers together."""
    return a + b

def multiply(x, y):
    """Multiply two numbers."""
    return x * y

def calculate_area(length, width):
    """Calculate area of a rectangle."""
    return multiply(length, width)

def greet(name):
    """Greet a person by name."""
    return f"Hello, {name}!"

def main():
    """Main function that calls other functions."""
    # Test basic arithmetic
    result1 = add(5, 3)
    result2 = multiply(4, 6)
    
    # Test area calculation
    area = calculate_area(10, 20)
    
    # Test greeting
    message = greet("World")
    
    print(f"Addition result: {result1}")
    print(f"Multiplication result: {result2}")
    print(f"Area: {area}")
    print(message)
    
    return True

if __name__ == "__main__":
    main()
