import { ParsedFunction, ParsedCall, CodeParseResult } from './parseFile.js';
import { v4 as uuidv4 } from 'uuid';

export interface CallGraphEdge {
  '@id': string;
  '@type': 'workflow_calls';
  source: string;
  target: string;
  call_type: 'direct';
  confidence: number;
  file: string;
  line: number;
}

export interface CallGraphResult {
  edges: CallGraphEdge[];
  stats: {
    functionsFound: number;
    callsFound: number;
    edgesCreated: number;
    unresolvedCalls: number;
  };
}

/**
 * Extract call graph edges from parsed code results
 */
export function extractCallGraph(
  parseResult: CodeParseResult
): CallGraphResult {
  const edges: CallGraphEdge[] = [];
  const stats = {
    functionsFound: parseResult.functions.length,
    callsFound: parseResult.calls.length,
    edgesCreated: 0,
    unresolvedCalls: 0,
  };

  // Create a map of function names to their IDs for quick lookup
  const functionMap = new Map<string, ParsedFunction[]>();

  for (const func of parseResult.functions) {
    if (!functionMap.has(func.name)) {
      functionMap.set(func.name, []);
    }
    functionMap.get(func.name)!.push(func);
  }

  // Process each call to create edges
  for (const call of parseResult.calls) {
    const callerFunction = findContainingFunction(call, parseResult.functions);
    const targetFunctions = functionMap.get(call.callee) || [];

    if (callerFunction && targetFunctions.length > 0) {
      // For M1, only create edges for same-file calls (as per key decisions)
      const sameFileTargets = targetFunctions.filter(
        (target) => target.file === call.file
      );

      for (const target of sameFileTargets) {
        const edge: CallGraphEdge = {
          '@id': `call:${uuidv4()}`,
          '@type': 'workflow_calls',
          source: callerFunction.id,
          target: target.id,
          call_type: 'direct',
          confidence: 1.0,
          file: call.file,
          line: call.line,
        };

        edges.push(edge);
        stats.edgesCreated++;
      }
    } else {
      stats.unresolvedCalls++;
    }
  }

  return { edges, stats };
}

/**
 * Find the function that contains a given call
 */
function findContainingFunction(
  call: ParsedCall,
  functions: ParsedFunction[]
): ParsedFunction | null {
  // Find functions in the same file
  const sameFunctions = functions.filter((func) => func.file === call.file);

  // Find the function that contains this call line
  for (const func of sameFunctions) {
    if (call.line >= func.line_start && call.line <= func.line_end) {
      return func;
    }
  }

  return null;
}

/**
 * Merge call graph results from multiple parse results
 */
export function mergeCallGraphs(results: CallGraphResult[]): CallGraphResult {
  const allEdges: CallGraphEdge[] = [];
  const mergedStats = {
    functionsFound: 0,
    callsFound: 0,
    edgesCreated: 0,
    unresolvedCalls: 0,
  };

  for (const result of results) {
    allEdges.push(...result.edges);
    mergedStats.functionsFound += result.stats.functionsFound;
    mergedStats.callsFound += result.stats.callsFound;
    mergedStats.edgesCreated += result.stats.edgesCreated;
    mergedStats.unresolvedCalls += result.stats.unresolvedCalls;
  }

  // Remove duplicate edges (same source, target, file, line)
  const uniqueEdges = deduplicateEdges(allEdges);
  mergedStats.edgesCreated = uniqueEdges.length;

  return {
    edges: uniqueEdges,
    stats: mergedStats,
  };
}

/**
 * Remove duplicate call graph edges
 */
function deduplicateEdges(edges: CallGraphEdge[]): CallGraphEdge[] {
  const seen = new Set<string>();
  const unique: CallGraphEdge[] = [];

  for (const edge of edges) {
    const key = `${edge.source}:${edge.target}:${edge.file}:${edge.line}`;

    if (!seen.has(key)) {
      seen.add(key);
      unique.push(edge);
    }
  }

  return unique;
}

/**
 * Filter call graph edges by confidence threshold
 */
export function filterByConfidence(
  result: CallGraphResult,
  minConfidence: number
): CallGraphResult {
  const filteredEdges = result.edges.filter(
    (edge) => edge.confidence >= minConfidence
  );

  return {
    edges: filteredEdges,
    stats: {
      ...result.stats,
      edgesCreated: filteredEdges.length,
    },
  };
}

/**
 * Get call graph statistics
 */
export function getCallGraphStats(result: CallGraphResult): {
  totalEdges: number;
  averageConfidence: number;
  filesCovered: number;
  uniqueCallers: number;
  uniqueCallees: number;
} {
  const edges = result.edges;
  const totalEdges = edges.length;

  if (totalEdges === 0) {
    return {
      totalEdges: 0,
      averageConfidence: 0,
      filesCovered: 0,
      uniqueCallers: 0,
      uniqueCallees: 0,
    };
  }

  const averageConfidence =
    edges.reduce((sum, edge) => sum + edge.confidence, 0) / totalEdges;
  const filesCovered = new Set(edges.map((edge) => edge.file)).size;
  const uniqueCallers = new Set(edges.map((edge) => edge.source)).size;
  const uniqueCallees = new Set(edges.map((edge) => edge.target)).size;

  return {
    totalEdges,
    averageConfidence,
    filesCovered,
    uniqueCallers,
    uniqueCallees,
  };
}
