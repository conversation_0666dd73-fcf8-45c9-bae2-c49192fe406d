import { parseSpecFile, parseSpecsDirectory } from './parse-specs.js';
import { writeFileSync, mkdirSync, rmSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

describe('parseSpecFile', () => {
  const testDir = join(tmpdir(), 'spec-parser-test');

  beforeEach(() => {
    // Clean up and create test directory
    rmSync(testDir, { recursive: true, force: true });
    mkdirSync(testDir, { recursive: true });
  });

  afterEach(() => {
    // Clean up test directory
    rmSync(testDir, { recursive: true, force: true });
  });

  it('should parse MDX file with frontmatter and headings', () => {
    const testContent = `---
title: Test Milestone
description: A test milestone for parsing
version: 1.0.0
status: Draft
tags: [test, milestone]
authors: [TestAuthor]
---

# Main Heading

This is some content.

## Sub Heading

More content here.

### Deep Heading

Even more content.
`;

    const testFile = join(testDir, 'test.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.id).toBeDefined();
    expect(result.filePath).toBe(testFile);
    expect(result.frontmatter.title).toBe('Test Milestone');
    expect(result.frontmatter.description).toBe('A test milestone for parsing');
    expect(result.frontmatter.version).toBe('1.0.0');
    expect(result.frontmatter.status).toBe('Draft');
    expect(result.frontmatter.tags).toEqual(['test', 'milestone']);
    expect(result.frontmatter.authors).toEqual(['TestAuthor']);

    expect(result.headings).toHaveLength(3);
    expect(result.headings[0]).toEqual({
      level: 1,
      text: 'Main Heading',
      id: 'main-heading',
    });
    expect(result.headings[1]).toEqual({
      level: 2,
      text: 'Sub Heading',
      id: 'sub-heading',
    });
    expect(result.headings[2]).toEqual({
      level: 3,
      text: 'Deep Heading',
      id: 'deep-heading',
    });
  });

  it('should handle file without frontmatter', () => {
    const testContent = `# Simple Heading

Just some content without frontmatter.
`;

    const testFile = join(testDir, 'simple.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.id).toBeDefined();
    expect(result.filePath).toBe(testFile);
    expect(result.frontmatter).toEqual({});
    expect(result.headings).toHaveLength(1);
    expect(result.headings[0]?.text).toBe('Simple Heading');
  });

  it('should handle empty file', () => {
    const testFile = join(testDir, 'empty.mdx');
    writeFileSync(testFile, '');

    const result = parseSpecFile(testFile);

    expect(result.id).toBeDefined();
    expect(result.filePath).toBe(testFile);
    expect(result.frontmatter).toEqual({});
    expect(result.headings).toHaveLength(0);
    expect(result.content).toBe('');
  });

  it('should handle headings with special characters', () => {
    const testContent = `# Heading with Special Characters! @#$%
## Another Heading (with parentheses)
### Heading with "quotes" and 'apostrophes'
`;

    const testFile = join(testDir, 'special.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.headings).toHaveLength(3);
    expect(result.headings[0]?.id).toBe('heading-with-special-characters');
    expect(result.headings[1]?.id).toBe('another-heading-with-parentheses');
    expect(result.headings[2]?.id).toBe('heading-with-quotes-and-apostrophes');
  });

  it('should generate deterministic IDs', () => {
    const testContent = `---
title: Test File
---
# Heading
`;

    const testFile = join(testDir, 'deterministic.mdx');
    writeFileSync(testFile, testContent);

    const result1 = parseSpecFile(testFile);
    const result2 = parseSpecFile(testFile);

    expect(result1.id).toBe(result2.id);
    expect(result1.id).toContain('test-file');
  });

  it('should handle file without title in frontmatter', () => {
    const testContent = `---
description: File without title
---
# Heading
`;

    const testFile = join(testDir, 'no-title.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.id).toBeDefined();
    expect(result.id).toContain('no-title');
  });

  it('should handle edge cases in heading ID generation', () => {
    const testContent = `## ---Special---Characters---
### 123 Numbers Only
#### !@#$%^&*()
##### Empty heading text
`;

    const testFile = join(testDir, 'edge-headings.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.headings).toHaveLength(4);
    expect(result.headings[0]?.id).toBe('specialcharacters');
    expect(result.headings[1]?.id).toBe('123-numbers-only');
    expect(result.headings[2]?.id).toBe('');
    expect(result.headings[3]?.id).toBe('empty-heading-text');
  });
});

describe('parseSpecsDirectory', () => {
  const testDir = join(tmpdir(), 'spec-parser-dir-test');

  beforeEach(() => {
    // Clean up and create test directory
    rmSync(testDir, { recursive: true, force: true });
    mkdirSync(testDir, { recursive: true });
  });

  afterEach(() => {
    // Clean up test directory
    rmSync(testDir, { recursive: true, force: true });
  });

  it('should parse multiple MDX files in directory', () => {
    // Create test files
    const file1Content = `---
title: File 1
---
# Heading 1
`;
    const file2Content = `---
title: File 2
---
# Heading 2
`;

    writeFileSync(join(testDir, 'file1.mdx'), file1Content);
    writeFileSync(join(testDir, 'file2.mdx'), file2Content);
    writeFileSync(join(testDir, 'not-mdx.txt'), 'This should be ignored');

    const result = parseSpecsDirectory(testDir);

    expect(result.errors).toHaveLength(0);
    expect(result.specs).toHaveLength(2);

    const titles = result.specs.map((spec) => spec.frontmatter.title);
    expect(titles).toContain('File 1');
    expect(titles).toContain('File 2');
  });

  it('should handle nested directories', () => {
    // Create nested structure
    mkdirSync(join(testDir, 'subdir'), { recursive: true });

    const file1Content = `---
title: Root File
---
# Root Heading
`;
    const file2Content = `---
title: Nested File
---
# Nested Heading
`;

    writeFileSync(join(testDir, 'root.mdx'), file1Content);
    writeFileSync(join(testDir, 'subdir', 'nested.mdx'), file2Content);

    const result = parseSpecsDirectory(testDir);

    expect(result.errors).toHaveLength(0);
    expect(result.specs).toHaveLength(2);

    const titles = result.specs.map((spec) => spec.frontmatter.title);
    expect(titles).toContain('Root File');
    expect(titles).toContain('Nested File');
  });

  it('should handle empty directory', () => {
    const result = parseSpecsDirectory(testDir);

    expect(result.errors).toHaveLength(0);
    expect(result.specs).toHaveLength(0);
  });

  it('should handle invalid MDX file and report errors', () => {
    // Create a file that will cause parsing issues
    const invalidContent = `---
title: Invalid File
invalid-yaml: [unclosed array
---
# Heading
`;

    writeFileSync(join(testDir, 'invalid.mdx'), invalidContent);

    const result = parseSpecsDirectory(testDir);

    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]?.filePath).toContain('invalid.mdx');
    expect(result.errors[0]?.error).toBe('Failed to parse file');
    expect(result.specs).toHaveLength(0);
  });

  it('should handle non-existent directory', () => {
    const nonExistentDir = join(testDir, 'does-not-exist');

    const result = parseSpecsDirectory(nonExistentDir);

    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]?.filePath).toBe(nonExistentDir);
    expect(result.errors[0]?.error).toBe('Failed to read directory');
    expect(result.specs).toHaveLength(0);
  });

  it('should handle directory read errors with Error objects', () => {
    // Test the Error instanceof Error branch (line 68)
    const nonExistentDir = join(testDir, 'definitely-does-not-exist');

    const result = parseSpecsDirectory(nonExistentDir);

    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]?.filePath).toBe(nonExistentDir);
    expect(result.errors[0]?.error).toBe('Failed to read directory');
    expect(result.errors[0]?.details).toContain('ENOENT'); // Should contain error message
    expect(result.specs).toHaveLength(0);
  });

  it('should handle file with malformed path edge cases', () => {
    // Create a file with a path that will test the edge cases in generateSpecId
    const testContent = `---
description: File without title
---
# Heading
`;

    // Test file without .mdx extension in the path logic
    const testFile = join(testDir, 'edge-case');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    expect(result.id).toBeDefined();
    expect(result.id).toContain('edge-case');
  });

  it('should handle files that cause parsing errors', () => {
    // Create a valid file
    const validContent = `---
title: Valid File
---
# Valid Heading
`;
    writeFileSync(join(testDir, 'valid.mdx'), validContent);

    // Create a file with malformed YAML frontmatter that will cause gray-matter to throw
    const problematicFile = join(testDir, 'problematic.mdx');
    const invalidContent = `---
title: Invalid
invalid: yaml: content: [unclosed array
---
# Content`;
    writeFileSync(problematicFile, invalidContent);

    const result = parseSpecsDirectory(testDir);

    // Should have one valid spec and one error
    expect(result.specs).toHaveLength(1);
    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]?.filePath).toBe(problematicFile);
    expect(result.errors[0]?.error).toBe('Failed to parse file');
    expect(result.errors[0]?.details).toBeDefined();
  });

  it('should handle edge case where file path has no extractable filename', () => {
    // Test the fallback to 'unknown' in generateSpecId
    // This tests line 153 where both frontmatter.title and filename extraction fail
    const testContent = `---
description: File without title and problematic path
---
# Heading
`;

    // Test the actual case - file without title should use filename
    const testFile = join(testDir, 'test.mdx');
    writeFileSync(testFile, testContent);

    const result = parseSpecFile(testFile);

    // The file should parse successfully and generate an ID
    expect(result.id).toBeDefined();
    expect(result.frontmatter.title).toBeUndefined();
    expect(result.id).toContain('test'); // Should use filename since no title
  });

  it('should handle file path edge cases in generateSpecId', () => {
    // Test various edge cases for the filename extraction logic
    const testContent = `---
description: No title provided
---
# Content
`;

    // Test with a path that has multiple extensions
    const complexFile = join(testDir, 'file.backup.mdx');
    writeFileSync(complexFile, testContent);

    const result = parseSpecFile(complexFile);

    expect(result.id).toBeDefined();
    expect(result.id).toContain('file-backup'); // Should handle multiple dots
  });
});
