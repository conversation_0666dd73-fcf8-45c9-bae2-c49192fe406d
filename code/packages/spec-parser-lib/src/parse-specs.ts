import matter from 'gray-matter';
import { readFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';

export interface SpecFrontmatter {
  title?: string;
  description?: string;
  created?: string;
  updated?: string;
  version?: string;
  status?: string;
  tags?: string[];
  authors?: string[];
  [key: string]: unknown;
}

export interface ParsedSpec {
  id: string;
  filePath: string;
  frontmatter: SpecFrontmatter;
  content: string;
  headings: Heading[];
}

export interface Heading {
  level: number;
  text: string;
  id: string;
}

export interface ParseResult {
  specs: ParsedSpec[];
  errors: ParseError[];
}

export interface ParseError {
  filePath: string;
  error: string;
  details?: string;
}

/**
 * Parse all MDX files in a directory recursively
 */
export function parseSpecsDirectory(directoryPath: string): ParseResult {
  const specs: ParsedSpec[] = [];
  const errors: ParseError[] = [];

  try {
    const files = findMdxFiles(directoryPath);

    for (const filePath of files) {
      try {
        const spec = parseSpecFile(filePath);
        specs.push(spec);
      } catch (error) {
        errors.push({
          filePath,
          error: 'Failed to parse file',
          details: error instanceof Error ? error.message : String(error),
        });
      }
    }
  } catch (error) {
    errors.push({
      filePath: directoryPath,
      error: 'Failed to read directory',
      details: error instanceof Error ? error.message : String(error),
    });
  }

  return { specs, errors };
}

/**
 * Parse a single MDX file
 */
export function parseSpecFile(filePath: string): ParsedSpec {
  const content = readFileSync(filePath, 'utf-8');
  const parsed = matter(content);

  const headings = extractHeadings(parsed.content);

  return {
    id: generateSpecId(filePath, parsed.data),
    filePath,
    frontmatter: parsed.data as SpecFrontmatter,
    content: parsed.content,
    headings,
  };
}

/**
 * Find all MDX files in a directory recursively
 */
function findMdxFiles(directoryPath: string): string[] {
  const files: string[] = [];

  function traverse(currentPath: string) {
    const items = readdirSync(currentPath);

    for (const item of items) {
      const fullPath = join(currentPath, item);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (stat.isFile() && extname(item) === '.mdx') {
        files.push(fullPath);
      }
    }
  }

  traverse(directoryPath);
  return files;
}

/**
 * Extract headings from markdown content
 */
function extractHeadings(content: string): Heading[] {
  const headings: Heading[] = [];
  const lines = content.split('\n');

  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      // TypeScript doesn't know regex guarantees these exist, so we use non-null assertion
      const level = match[1]!.length;
      const text = match[2]!.trim();
      const id = generateHeadingId(text);

      headings.push({
        level,
        text,
        id,
      });
    }
  }

  return headings;
}

/**
 * Generate a deterministic ID for a spec
 */
function generateSpecId(
  filePath: string,
  frontmatter: SpecFrontmatter
): string {
  // Use title from frontmatter if available, otherwise use filename
  const title =
    frontmatter.title ||
    filePath.split('/').pop()?.replace('.mdx', '') ||
    'unknown';

  // Create a deterministic ID based on file path and title
  const seed = `${filePath}-${title}`;

  // For now, use a simple hash-like approach
  // In production, you might want to use a proper deterministic UUID library
  return `spec-${seed.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`;
}

/**
 * Generate a heading ID from text
 */
function generateHeadingId(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '');
}
