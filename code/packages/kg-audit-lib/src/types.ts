/**
 * @fileoverview Type definitions for kg-audit-lib
 */

import type { MilestoneCoverage } from '@workflow-mapper/kg-sync-lib';

/**
 * @implements milestone-M2#AuditLib
 * Type registry and validation for audit operations
 */
export function validateAuditTypes(data: unknown): data is AuditReport {
  // Type validation logic
  return typeof data === 'object' && data !== null && 'summary' in data;
}

/**
 * Options for audit operations
 */
export interface AuditOptions {
  since?: string;
  threshold?: number;
  includePerformance?: boolean;
  format?: 'json' | 'pretty' | 'both';
}

/**
 * Unknown edge detected in the knowledge graph
 */
export interface UnknownEdge {
  type: 'workflow_calls' | 'implements';
  source: string;
  target: string;
  confidence: number;
  reason: 'missing_target' | 'stale_spec' | 'parse_error';
  filePath?: string;
  lineNumber?: number;
}

export interface AuditEdge {
  '@id': string;
  '@type': 'workflow_calls' | 'implements' | 'dependsOn';
  source: string;
  target: string;
  filePath?: string;
  lineNumber?: number;
}

/**
 * Extended milestone coverage with audit-specific metrics
 */
export interface AuditCoverage extends MilestoneCoverage {
  unknownEdgeCount: number;
  auditTimestamp: string;
  components: {
    total: number;
    implemented: number;
    stale: number;
  };
}

/**
 * Complete audit report structure
 */
export interface AuditReport {
  summary: {
    generatedAt: string;
    edgeTotals: {
      implements: number;
      workflow_calls: number;
      dependsOn: number;
      total: number;
    };
    gitRef: string;
    filesScanned: number;
  };
  milestones: AuditCoverage[];
  unknownEdges: UnknownEdge[];
  performance: {
    durationMs: number;
    filesProcessed: number;
    edgesAnalyzed: number;
    cacheHits?: number;
  };
}

/**
 * Audit report validation result
 */
export interface AuditValidationResult {
  isValid: boolean;
  errors: string[];
}
