/**
 * @fileoverview Tests for kg-audit-lib type definitions and validation
 * @implements milestone-M2#AuditLib
 */

import { validateAuditTypes } from '../src/types.js';
import type {
  AuditReport,
  UnknownEdge,
  AuditCoverage,
  AuditOptions,
} from '../src/types.js';

describe('kg-audit-lib Types', () => {
  describe('validateAuditTypes', () => {
    it('should validate a valid audit report', () => {
      const validReport: AuditReport = {
        summary: {
          generatedAt: new Date().toISOString(),
          edgeTotals: {
            implements: 5,
            workflow_calls: 3,
            dependsOn: 2,
            total: 10,
          },
          gitRef: 'HEAD',
          filesScanned: 100,
        },
        milestones: [],
        unknownEdges: [],
        performance: {
          durationMs: 1500,
          filesProcessed: 100,
          edgesAnalyzed: 250,
        },
      };

      expect(validateAuditTypes(validReport)).toBe(true);
    });

    it('should reject null or undefined', () => {
      expect(validateAuditTypes(null)).toBe(false);
      expect(validateAuditTypes(undefined)).toBe(false);
    });

    it('should reject primitive types', () => {
      expect(validateAuditTypes('string')).toBe(false);
      expect(validateAuditTypes(123)).toBe(false);
      expect(validateAuditTypes(true)).toBe(false);
    });

    it('should reject objects without summary', () => {
      expect(validateAuditTypes({})).toBe(false);
      expect(validateAuditTypes({ milestones: [] })).toBe(false);
      expect(validateAuditTypes({ unknownEdges: [] })).toBe(false);
    });

    it('should accept objects with summary property', () => {
      expect(validateAuditTypes({ summary: {} })).toBe(true);
      expect(
        validateAuditTypes({ summary: { generatedAt: '2023-01-01' } })
      ).toBe(true);
    });
  });

  describe('Type Interfaces', () => {
    it('should allow valid AuditOptions', () => {
      const options1: AuditOptions = {};
      const options2: AuditOptions = {
        since: 'abc123',
        threshold: 0.8,
        includePerformance: true,
        format: 'json',
      };
      const options3: AuditOptions = {
        format: 'pretty',
      };

      // If these compile without TypeScript errors, the test passes
      expect(options1).toBeDefined();
      expect(options2).toBeDefined();
      expect(options3).toBeDefined();
    });

    it('should allow valid UnknownEdge', () => {
      const edge1: UnknownEdge = {
        type: 'workflow_calls',
        source: 'function1',
        target: 'function2',
        confidence: 0.5,
        reason: 'missing_target',
      };

      const edge2: UnknownEdge = {
        type: 'implements',
        source: 'function1',
        target: 'component1',
        confidence: 0.3,
        reason: 'stale_spec',
        filePath: 'src/test.ts',
        lineNumber: 42,
      };

      // If these compile without TypeScript errors, the test passes
      expect(edge1).toBeDefined();
      expect(edge2).toBeDefined();
    });

    it('should allow valid AuditCoverage', () => {
      const coverage: AuditCoverage = {
        milestoneId: 'M1.1',
        coverage: 0.85,
        confidence: 0.92,
        lastUpdated: new Date().toISOString(),
        unknownEdgeCount: 2,
        auditTimestamp: new Date().toISOString(),
        components: {
          total: 10,
          implemented: 8,
          stale: 1,
        },
        totalComponents: 10,
        implementedComponents: 8,
      };

      // If this compiles without TypeScript errors, the test passes
      expect(coverage).toBeDefined();
      expect(coverage.milestoneId).toBe('M1.1');
      expect(coverage.unknownEdgeCount).toBe(2);
      expect(coverage.components.total).toBe(10);
    });

    it('should allow valid AuditReport', () => {
      const report: AuditReport = {
        summary: {
          generatedAt: new Date().toISOString(),
          edgeTotals: {
            implements: 5,
            workflow_calls: 3,
            dependsOn: 2,
            total: 10,
          },
          gitRef: 'HEAD',
          filesScanned: 100,
        },
        milestones: [
          {
            milestoneId: 'M1.1',
            coverage: 0.85,
            confidence: 0.92,
            lastUpdated: new Date().toISOString(),
            unknownEdgeCount: 1,
            auditTimestamp: new Date().toISOString(),
            components: {
              total: 10,
              implemented: 8,
              stale: 1,
            },
            totalComponents: 10,
            implementedComponents: 8,
          },
        ],
        unknownEdges: [
          {
            type: 'workflow_calls',
            source: 'function1',
            target: 'missing_function',
            confidence: 0.2,
            reason: 'missing_target',
          },
        ],
        performance: {
          durationMs: 1500,
          filesProcessed: 100,
          edgesAnalyzed: 250,
          cacheHits: 50,
        },
      };

      // If this compiles without TypeScript errors, the test passes
      expect(report).toBeDefined();
      expect(report.summary.edgeTotals.total).toBe(10);
      expect(report.milestones).toHaveLength(1);
      expect(report.unknownEdges).toHaveLength(1);
    });
  });
});
