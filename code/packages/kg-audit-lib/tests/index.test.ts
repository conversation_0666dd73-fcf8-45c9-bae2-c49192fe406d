/**
 * @fileoverview Tests for kg-audit-lib main index module
 * @implements milestone-M2#AuditLib
 */

// Mock kg-sync-lib to avoid ES module issues
jest.mock('@workflow-mapper/kg-sync-lib', () => ({
  calculateCoverage: jest.fn(),
}));

import { initializeAuditLibrary } from '../src/index.js';

describe('kg-audit-lib Index', () => {
  describe('initializeAuditLibrary', () => {
    it('should initialize the audit library with correct configuration', () => {
      const result = initializeAuditLibrary();

      expect(result).toBeDefined();
      expect(result.version).toBe('1.0.0');
      expect(result.capabilities).toBeDefined();
      expect(Array.isArray(result.capabilities)).toBe(true);
    });

    it('should include all expected capabilities', () => {
      const result = initializeAuditLibrary();

      const expectedCapabilities = [
        'coverage',
        'unknown-edges',
        'audit-reports',
        'schema-validation',
      ];

      expect(result.capabilities).toEqual(expectedCapabilities);
    });

    it('should return consistent results on multiple calls', () => {
      const result1 = initializeAuditLibrary();
      const result2 = initializeAuditLibrary();

      expect(result1).toEqual(result2);
    });
  });

  describe('Module Exports', () => {
    it('should export all required functions', async () => {
      const module = await import('../src/index.js');

      // Check that all expected exports are present
      expect(typeof module.initializeAuditLibrary).toBe('function');
      expect(typeof module.calculateAuditCoverage).toBe('function');
      expect(typeof module.calculateAllMilestoneCoverage).toBe('function');
      expect(typeof module.detectUnknownEdges).toBe('function');
      expect(typeof module.groupUnknownEdgesByMilestone).toBe('function');
      expect(typeof module.generateAuditReport).toBe('function');
      expect(typeof module.validateAuditReport).toBe('function');
    });

    it('should have proper TypeScript type exports', async () => {
      // This test ensures the module can be imported without TypeScript errors
      const module = await import('../src/index.js');

      // If this test passes, it means all type exports are working correctly
      expect(module).toBeDefined();
    });
  });
});
