/**
 * @fileoverview Tests for audit coverage calculation functionality
 * @implements milestone-M2#CoverageCalculation
 */

import {
  calculateAuditCoverage,
  calculateAllMilestoneCoverage,
} from '../src/coverage.js';
import { calculateCoverage } from '@workflow-mapper/kg-sync-lib';
import type { KnowledgeGraphEdge } from '../src/index.js';

// Mock kg-sync-lib calculateCoverage function
jest.mock('@workflow-mapper/kg-sync-lib', () => ({
  calculateCoverage: jest.fn(),
}));

const mockCalculateCoverage = calculateCoverage as jest.MockedFunction<
  typeof calculateCoverage
>;

describe('Audit Coverage Calculation', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for base coverage calculation
    mockCalculateCoverage.mockReturnValue({
      milestoneId: 'M1.2',
      totalComponents: 10,
      implementedComponents: 8,
      coverage: 0.8,
      confidence: 0.9,
      lastUpdated: '2023-01-01T00:00:00.000Z',
    });
  });

  const createEdge = (
    milestoneId: string,
    componentName: string,
    confidence: number,
    filePath?: string
  ): KnowledgeGraphEdge => ({
    '@id': `implements:function:${filePath || 'src/test.ts'}#testFunction->component:${milestoneId}#${componentName}`,
    '@type': 'implements',
    source: `function:${filePath || 'src/test.ts'}#testFunction`,
    target: `component:${milestoneId}#${componentName}`,
    confidence,
    lastVerified: new Date().toISOString(),
  });

  describe('calculateAuditCoverage', () => {
    it('should extend base coverage with audit metrics', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 0.8),
        createEdge('M1.2', 'ComponentC', 0.1), // Stale component
      ];

      const result = calculateAuditCoverage('M1.2', edges, 10, 2);

      // Should call base coverage calculation
      expect(mockCalculateCoverage).toHaveBeenCalledWith('M1.2', edges, 10);

      // Should extend with audit-specific fields
      expect(result.milestoneId).toBe('M1.2');
      expect(result.coverage).toBe(0.8); // From base calculation
      expect(result.confidence).toBe(0.9); // From base calculation
      expect(result.unknownEdgeCount).toBe(2);
      expect(result.auditTimestamp).toBeDefined();
      expect(result.components).toEqual({
        total: 10,
        implemented: 8, // From base calculation
        stale: 1, // One component with confidence <= 0.2
      });
    });

    it('should calculate stale components correctly', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 0.2), // Stale
        createEdge('M1.2', 'ComponentC', 0.1), // Stale
        createEdge('M1.2', 'ComponentD', 0.8),
        createEdge('M1.2', 'ComponentE', 0.0), // Stale
      ];

      const result = calculateAuditCoverage('M1.2', edges, 10, 0);

      expect(result.components.stale).toBe(3); // Three components with confidence <= 0.2
    });

    it('should handle edges from different milestones', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.3', 'ComponentB', 0.1), // Different milestone, should not count as stale
        createEdge('M1.2', 'ComponentC', 0.1), // Same milestone, should count as stale
      ];

      const result = calculateAuditCoverage('M1.2', edges, 5, 1);

      expect(result.components.stale).toBe(1); // Only ComponentC should count
    });

    it('should handle duplicate component names correctly', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 0.1, 'src/file1.ts'),
        createEdge('M1.2', 'ComponentA', 0.1, 'src/file2.ts'), // Same component, different file
        createEdge('M1.2', 'ComponentB', 0.1),
      ];

      const result = calculateAuditCoverage('M1.2', edges, 5, 0);

      expect(result.components.stale).toBe(2); // ComponentA and ComponentB (unique components)
    });

    it('should handle empty edges array', () => {
      const result = calculateAuditCoverage('M1.2', [], 5, 0);

      expect(result.components.stale).toBe(0);
      expect(result.unknownEdgeCount).toBe(0);
      expect(result.auditTimestamp).toBeDefined();
    });

    it('should handle zero unknown edge count by default', () => {
      const edges = [createEdge('M1.2', 'ComponentA', 1.0)];
      const result = calculateAuditCoverage('M1.2', edges, 5);

      expect(result.unknownEdgeCount).toBe(0);
    });

    it('should generate valid ISO timestamp', () => {
      const edges = [createEdge('M1.2', 'ComponentA', 1.0)];
      const result = calculateAuditCoverage('M1.2', edges, 5, 1);

      expect(result.auditTimestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
      expect(new Date(result.auditTimestamp).getTime()).toBeGreaterThan(
        Date.now() - 1000
      );
    });
  });

  describe('calculateAllMilestoneCoverage', () => {
    beforeEach(() => {
      // Mock different responses for different milestones
      mockCalculateCoverage.mockImplementation((milestoneId: string) => ({
        milestoneId,
        totalComponents: milestoneId === 'M1.1' ? 5 : 10,
        implementedComponents: milestoneId === 'M1.1' ? 4 : 8,
        coverage: milestoneId === 'M1.1' ? 0.8 : 0.8,
        confidence: 0.9,
        lastUpdated: '2023-01-01T00:00:00.000Z',
      }));
    });

    it('should calculate coverage for all milestones', () => {
      const edges = [
        createEdge('M1.1', 'ComponentA', 1.0),
        createEdge('M1.1', 'ComponentB', 0.8),
        createEdge('M1.2', 'ComponentC', 1.0),
        createEdge('M1.2', 'ComponentD', 0.1), // Stale
      ];

      const milestoneComponents = new Map([
        ['M1.1', 5],
        ['M1.2', 10],
      ]);

      const unknownEdgesByMilestone = new Map([
        ['M1.1', 1],
        ['M1.2', 3],
      ]);

      const results = calculateAllMilestoneCoverage(
        edges,
        milestoneComponents,
        unknownEdgesByMilestone
      );

      expect(results).toHaveLength(2);

      const m11Result = results.find((r) => r.milestoneId === 'M1.1');
      expect(m11Result).toBeDefined();
      expect(m11Result!.unknownEdgeCount).toBe(1);
      expect(m11Result!.components.stale).toBe(0); // No stale components in M1.1

      const m12Result = results.find((r) => r.milestoneId === 'M1.2');
      expect(m12Result).toBeDefined();
      expect(m12Result!.unknownEdgeCount).toBe(3);
      expect(m12Result!.components.stale).toBe(1); // ComponentD is stale
    });

    it('should handle milestones with no edges', () => {
      const edges: KnowledgeGraphEdge[] = [];
      const milestoneComponents = new Map([
        ['M1.1', 5],
        ['M1.2', 10],
      ]);

      const results = calculateAllMilestoneCoverage(edges, milestoneComponents);

      expect(results).toHaveLength(2);
      results.forEach((result) => {
        expect(result.unknownEdgeCount).toBe(0);
        expect(result.components.stale).toBe(0);
      });
    });

    it('should handle milestones with no unknown edges', () => {
      const edges = [
        createEdge('M1.1', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 1.0),
      ];

      const milestoneComponents = new Map([
        ['M1.1', 5],
        ['M1.2', 10],
      ]);

      const results = calculateAllMilestoneCoverage(edges, milestoneComponents);

      expect(results).toHaveLength(2);
      results.forEach((result) => {
        expect(result.unknownEdgeCount).toBe(0);
      });
    });

    it('should handle empty milestone components map', () => {
      const edges = [createEdge('M1.1', 'ComponentA', 1.0)];
      const milestoneComponents = new Map<string, number>();

      const results = calculateAllMilestoneCoverage(edges, milestoneComponents);

      expect(results).toHaveLength(0);
    });

    it('should filter edges correctly by milestone', () => {
      const edges = [
        createEdge('M1.1', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 1.0),
        createEdge('M1.3', 'ComponentC', 1.0), // Not in milestoneComponents
      ];

      const milestoneComponents = new Map([
        ['M1.1', 5],
        ['M1.2', 10],
      ]);

      const results = calculateAllMilestoneCoverage(edges, milestoneComponents);

      expect(results).toHaveLength(2);
      expect(mockCalculateCoverage).toHaveBeenCalledTimes(2);

      // Verify correct edges were passed to each milestone
      const m11Call = mockCalculateCoverage.mock.calls.find(
        (call) => call[0] === 'M1.1'
      );
      expect(m11Call![1]).toHaveLength(1); // Only one edge for M1.1

      const m12Call = mockCalculateCoverage.mock.calls.find(
        (call) => call[0] === 'M1.2'
      );
      expect(m12Call![1]).toHaveLength(1); // Only one edge for M1.2
    });

    it('should generate valid audit timestamps', () => {
      const edges = [createEdge('M1.1', 'ComponentA', 1.0)];
      const milestoneComponents = new Map([['M1.1', 5]]);

      const results = calculateAllMilestoneCoverage(edges, milestoneComponents);

      expect(results[0]!.auditTimestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
      expect(new Date(results[0]!.auditTimestamp).getTime()).toBeGreaterThan(
        Date.now() - 1000
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed edge targets gracefully', () => {
      const malformedEdge = {
        ...createEdge('M1.2', 'ComponentA', 1.0),
        target: 'malformed-target-without-milestone',
      };

      const result = calculateAuditCoverage('M1.2', [malformedEdge], 5, 0);

      expect(result.components.stale).toBe(0); // Should not crash, should not count malformed edge
    });

    it('should handle edges with missing confidence', () => {
      const edgeWithoutConfidence = {
        ...createEdge('M1.2', 'ComponentA', 1.0),
        confidence: undefined as unknown as number,
      };

      const result = calculateAuditCoverage(
        'M1.2',
        [edgeWithoutConfidence],
        5,
        0
      );

      expect(result.components.stale).toBe(0); // Should handle gracefully
    });
  });
});
