{"name": "@workflow-mapper/kg-audit-lib", "version": "0.0.1", "description": "Audit library for knowledge graph coverage and confidence metrics", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": ["knowledge-graph", "audit", "coverage", "confidence", "unknown-edges", "metrics"], "author": "WorkflowMapper Team", "license": "ISC", "dependencies": {"@workflow-mapper/kg-sync-lib": "workspace:^", "@workflow-mapper/spec-parser-lib": "workspace:^", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "uuid": "9.0.0", "yaml": "2.3.2"}, "devDependencies": {"@types/jest": "29.5.12", "@types/node": "20.12.7", "@types/uuid": "10.0.0", "jest": "29.7.0", "ts-jest": "29.1.2", "tsup": "8.0.2", "typescript": "5.4.3"}}