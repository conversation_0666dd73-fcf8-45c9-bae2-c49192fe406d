{"name": "@workflow-mapper/kg-sync-lib", "version": "0.0.1", "description": "Bidirectional sync library for knowledge graph incremental updates", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": ["knowledge-graph", "sync", "incremental", "git-diff", "annotations", "bidirectional"], "author": "WorkflowMapper Team", "license": "ISC", "dependencies": {"simple-git": "3.22.0", "comment-parser": "1.4.0", "regexparam": "2.0.0", "@workflow-mapper/spec-parser-lib": "workspace:^", "uuid": "9.0.0", "yaml": "2.3.2"}, "devDependencies": {"@types/jest": "29.5.12", "@types/node": "20.12.7", "@types/uuid": "10.0.0", "jest": "29.7.0", "ts-jest": "29.1.2", "tsup": "8.0.2", "typescript": "5.4.3"}}