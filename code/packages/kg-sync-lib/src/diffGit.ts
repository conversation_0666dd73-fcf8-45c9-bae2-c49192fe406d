/**
 * @fileoverview Git diff detection using simple-git
 */

import { simpleGit } from 'simple-git';
import type { GitDiffResult, SyncOptions } from './types.js';

/**
 * Detect changed files using git diff
 * @implements milestone-M1.2#GitDiffCore
 * @param options - Sync options including since reference
 * @returns Promise resolving to git diff result
 */
export async function diffGit(options: SyncOptions): Promise<GitDiffResult> {
  const git = simpleGit();
  const result: GitDiffResult = {
    changedFiles: [],
    addedFiles: [],
    deletedFiles: [],
    renamedFiles: [],
    errors: [],
  };

  try {
    // Determine the comparison reference
    const since = options.since || 'HEAD~1';

    // Check if we're in a git repository
    const isRepo = await git.checkIsRepo();
    if (!isRepo) {
      result.errors.push('Not in a git repository');
      return result;
    }

    // Check for merge conflicts first
    const status = await git.status();
    if (status.conflicted.length > 0) {
      result.errors.push(
        `Merge conflicts detected in ${status.conflicted.length} files`
      );
      if (options.verbose) {
        console.warn(`Merge conflicts in: ${status.conflicted.join(', ')}`);
      }
      // Continue processing but note the conflicts
    }

    // Validate the since reference exists
    try {
      await git.revparse([since]);
    } catch (refError) {
      result.errors.push(`Invalid git reference: ${since}`);
      return result;
    }

    // Get the diff summary with find-renames option
    const diffSummary = await git.diffSummary([since, '--find-renames']);

    // Process each file in the diff
    for (const file of diffSummary.files) {
      const filePath = file.file;

      // Skip binary files and non-source files first
      if (file.binary) {
        if (options.verbose) {
          console.log(`Skipping binary file: ${filePath}`);
        }
        continue;
      }

      if (!isSourceFile(filePath)) {
        if (options.verbose) {
          console.log(`Skipping non-source file: ${filePath}`);
        }
        continue;
      }

      // Handle large files (>10k changes) - only for text files
      const totalChanges = file.insertions + file.deletions;
      if (totalChanges > 10000) {
        if (options.verbose) {
          console.warn(
            `Large diff detected in ${filePath}: ${totalChanges} changes`
          );
        }
      }

      // Categorize the file change
      if (file.insertions > 0 && file.deletions === 0) {
        result.addedFiles.push(filePath);
      } else if (file.insertions === 0 && file.deletions > 0) {
        result.deletedFiles.push(filePath);
      } else {
        result.changedFiles.push(filePath);
      }
    }

    // Check for renamed files using git status
    for (const file of status.renamed) {
      // Only include source file renames
      if (isSourceFile(file.from) || isSourceFile(file.to)) {
        result.renamedFiles.push({
          from: file.from,
          to: file.to,
        });
      }
    }

    // Check for untracked files that might be relevant
    const untrackedSourceFiles = status.not_added.filter(isSourceFile);
    if (untrackedSourceFiles.length > 0 && options.verbose) {
      console.log(
        `Untracked source files detected: ${untrackedSourceFiles.join(', ')}`
      );
    }

    if (options.verbose) {
      console.log(`Git diff detected (since ${since}):`);
      console.log(`  Changed: ${result.changedFiles.length} files`);
      console.log(`  Added: ${result.addedFiles.length} files`);
      console.log(`  Deleted: ${result.deletedFiles.length} files`);
      console.log(`  Renamed: ${result.renamedFiles.length} files`);
      if (result.errors.length > 0) {
        console.log(`  Errors: ${result.errors.length}`);
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Categorize different types of git errors
    if (errorMessage.includes('not a git repository')) {
      result.errors.push('Not in a git repository');
    } else if (errorMessage.includes('bad revision')) {
      result.errors.push(`Invalid git reference: ${options.since || 'HEAD~1'}`);
    } else if (errorMessage.includes('merge conflict')) {
      result.errors.push('Merge conflicts prevent diff operation');
    } else {
      result.errors.push(`Git diff failed: ${errorMessage}`);
    }

    if (options.verbose) {
      console.error(`Git diff error: ${errorMessage}`);
    }
  }

  return result;
}

/**
 * Check if a file is a source file that should be processed
 * @param filePath - Path to the file
 * @returns True if the file should be processed
 */
function isSourceFile(filePath: string): boolean {
  const sourceExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.mdx', '.md'];
  return sourceExtensions.some((ext) => filePath.endsWith(ext));
}
