# @workflow-mapper/kg-sync-lib

Bidirectional sync library for knowledge graph incremental updates with git diff integration.

## Overview

This package provides comprehensive functionality for maintaining bidirectional synchronization between source code annotations and milestone specifications. It enables incremental knowledge graph updates with confidence scoring and coverage metrics.

### Key Features

- **Annotation Parsing**: Parse `@implements milestone-ID#Component` annotations from TypeScript, JavaScript, and Python
- **Git Diff Integration**: Detect changed files since specified commit/branch for incremental processing
- **Knowledge Graph Updates**: Incrementally update knowledge graphs with confidence scoring
- **Coverage Metrics**: Calculate milestone implementation coverage with threshold enforcement
- **Performance Optimization**: 90%+ improvement over full repository scans
- **Comprehensive Error Handling**: Detailed error reporting with file context and validation
- **Bidirectional Sync**: Maintain consistency between code and specifications

## Installation

```bash
pnpm add @workflow-mapper/kg-sync-lib
```

## Quick Start

### Basic Usage

```typescript
import { syncKnowledgeGraph } from '@workflow-mapper/kg-sync-lib';

// Basic incremental sync
const result = await syncKnowledgeGraph('./docs/tech-specs', {
  since: 'origin/main',
  dryRun: false,
  outputDir: '.',
  verbose: true
});

console.log(`Exit code: ${result.exitCode}`);
console.log(`Coverage: ${result.coverageMetrics.length} milestones processed`);
console.log(`Performance: ${result.performance.filesProcessed} files processed`);
```

### Annotation Examples

```typescript
/**
 * @implements milestone-M1.2#AnnotationParser
 */
export function parseAnnotations(content: string, filePath: string): Annotation[] {
  // Parse JSDoc annotations from source code
  return annotations;
}

/**
 * @implements milestone-M1.2#GitDiffDetector
 */
class GitDiffDetector {
  /**
   * @implements milestone-M1.2#DiffAnalysis
   */
  async detectChanges(since: string): Promise<DiffResult> {
    // Detect changed files since specified commit
    return changes;
  }
}
```

### Advanced Usage

```typescript
import {
  diffGit,
  parseAnnotations,
  updateGraph,
  calculateCoverage
} from '@workflow-mapper/kg-sync-lib';

// Step-by-step workflow
const changedFiles = await diffGit({ since: 'HEAD~1' });
const annotations = [];

for (const file of changedFiles) {
  const content = await fs.readFile(file, 'utf-8');
  const fileAnnotations = parseAnnotations(content, file);
  annotations.push(...fileAnnotations);
}

const updatedGraph = updateGraph(existingGraph, annotations, changedFiles);
const coverage = calculateCoverage('M1.2', updatedGraph.edges, totalComponents);
```

## API Reference

### Core Functions

#### `syncKnowledgeGraph(directory: string, options: SyncOptions): Promise<SyncResult>`

Main orchestration function for bidirectional sync operations.

**Parameters:**
- `directory`: Path to specifications directory
- `options.since`: Git reference for incremental diff (e.g., 'origin/main', 'HEAD~1')
- `options.dryRun`: Boolean flag for validation without file writes
- `options.outputDir`: Directory for output files (default: current directory)
- `options.verbose`: Enable detailed logging
- `options.threshold`: Coverage threshold for validation (default: 0.5)

**Returns:** `SyncResult` with exit code, coverage metrics, and performance data

#### `diffGit(options: DiffOptions): Promise<string[]>`

Detect changed files using git diff integration.

**Parameters:**
- `options.since`: Git reference for comparison
- `options.cwd`: Working directory (default: process.cwd())

**Returns:** Array of changed file paths

#### `parseAnnotations(content: string, filePath: string): Annotation[]`

Parse `@implements` annotations from source code content.

**Parameters:**
- `content`: Source code content as string
- `filePath`: File path for error reporting

**Returns:** Array of parsed annotations with validation results

#### `updateGraph(graph: JsonLdGraph, annotations: Annotation[], files: string[]): GraphUpdateResult`

Update knowledge graph with new annotations and mark stale entries.

**Parameters:**
- `graph`: Existing JSON-LD graph structure
- `annotations`: Parsed annotations to integrate
- `files`: List of changed files for stale detection

**Returns:** Updated graph with change tracking

#### `calculateCoverage(milestoneId: string, edges: Edge[], totalComponents: number): MilestoneCoverage`

Calculate milestone implementation coverage metrics.

**Parameters:**
- `milestoneId`: Milestone identifier (e.g., 'M1.2')
- `edges`: Implementation edges from knowledge graph
- `totalComponents`: Total components defined in milestone

**Returns:** Coverage metrics with confidence scoring

### Types

```typescript
interface SyncOptions {
  since?: string;
  dryRun?: boolean;
  outputDir?: string;
  verbose?: boolean;
  threshold?: number;
}

interface SyncResult {
  success: boolean;
  exitCode: number;
  coverageMetrics: MilestoneCoverage[];
  errors: ParseError[];
  warnings: ParseError[];
  performance: PerformanceMetrics;
}

interface Annotation {
  milestoneId: string;
  componentName: string;
  functionName: string;
  filePath: string;
  lineNumber: number;
  confidence: number;
  lastVerified: string;
  errors: ParseError[];
}
```

See `src/types.ts` for complete type definitions.

## Performance

This library is optimized for incremental operations:

- **90%+ improvement** over full repository scans
- **Linear scaling** with number of changed files
- **Memory efficient** - <100MB for typical repositories
- **Fast annotation parsing** - 100 annotations in <10ms

## Error Handling

The library provides comprehensive error handling with specific exit codes:

- **Exit 0**: Success
- **Exit 60**: Coverage threshold breach (coverage < threshold)
- **Exit 70**: Parse errors in annotations
- **Exit 1**: General errors (git failures, file system issues)

## Configuration

### Environment Variables

- `KG_SYNC_DEBUG`: Enable debug logging
- `KG_SYNC_TIMEOUT`: Git operation timeout in milliseconds (default: 30000)

### Annotation Format

```typescript
/**
 * @implements milestone-M1.2#ComponentName
 */
```

**Requirements:**
- Must be in JSDoc/TSDoc comment block
- Must be attached to function, class, or method
- Milestone ID must match pattern: `milestone-M\d+(\.\d+)*`
- Component name must be valid identifier: `[A-Za-z_][A-Za-z0-9_]*`

## Development

```bash
# Install dependencies
pnpm install

# Build
pnpm build

# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Type check
pnpm type-check

# Lint
pnpm lint
```

### Testing

The package includes comprehensive test coverage:

- **Unit Tests**: 98.65% coverage
- **Integration Tests**: End-to-end CLI scenarios
- **Performance Tests**: Benchmarking and regression detection
- **Edge Cases**: Error handling and malformed input

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Make changes and add tests
4. Ensure all tests pass: `pnpm test`
5. Submit a pull request

## License

ISC
