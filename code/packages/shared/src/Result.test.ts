import { describe, it, expect } from 'vitest';
import { Ok, Err, Result } from './Result';

describe('Result Type', () => {
  it('should create successful result', () => {
    const result = Ok('test data');
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toBe('test data');
    }
  });

  it('should create error result', () => {
    const result = Err(new Error('test error'));
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.message).toBe('test error');
    }
  });

  it('should work with type guards', () => {
    const successResult: Result<string, Error> = Ok('success');
    const errorResult: Result<string, Error> = Err(new Error('failure'));

    if (successResult.success) {
      expect(successResult.data).toBe('success');
    }

    if (!errorResult.success) {
      expect(errorResult.error.message).toBe('failure');
    }
  });
});
