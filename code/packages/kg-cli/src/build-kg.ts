#!/usr/bin/env node

import { parseArgs } from 'node:util';
import { buildKnowledgeGraph } from './index.js';
import {
  parseCodeDirectory,
  extractCallGraph,
  type CodeParseResult,
  type CallGraphResult,
} from '@workflow-mapper/code-parser-lib';
import {
  parseAnnotations,
  type Annotation,
} from '@workflow-mapper/kg-sync-lib';
import { readFileSync } from 'fs';
import { join } from 'path';
import { glob } from 'glob';

interface CliArgs {
  'dry-run'?: boolean;
  code?: string;
  languages?: string;
  help?: boolean;
}

function showHelp() {
  console.log(`
Usage: build-kg [options] <directory>

Build a knowledge graph from MDX specification files and optionally source code.

Arguments:
  directory         Directory containing MDX files to parse

Options:
  --dry-run        Print summary without writing files
  --code <dir>     Directory to scan for source code (.py, .js files)
  --languages <list> Comma-separated list of languages (default: py,js)
  --help           Show this help message

Examples:
  build-kg docs/tech-specs
  build-kg --dry-run docs/tech-specs
  build-kg --code src/ docs/tech-specs
  build-kg --code tests/fixtures --dry-run docs/tech-specs
  build-kg --code src/ --languages py docs/tech-specs
`);
}

/**
 * Parse @implements annotations from all TypeScript/JavaScript files in a directory
 */
async function parseAnnotationsFromDirectory(
  codeDirectory: string,
  languages: string[]
): Promise<Annotation[]> {
  const annotations: Annotation[] = [];

  // Build glob patterns for supported languages
  const patterns: string[] = [];
  if (languages.includes('js')) {
    patterns.push('**/*.{js,jsx,ts,tsx}');
  }
  if (languages.includes('py')) {
    patterns.push('**/*.py');
  }

  if (patterns.length === 0) {
    return annotations;
  }

  try {
    // Find all matching files, excluding test files
    const files = await glob(patterns, {
      cwd: codeDirectory,
      ignore: [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/.git/**',
        '**/*.test.*',
        '**/*.spec.*',
      ],
    });

    console.log(
      `🔍 Scanning ${files.length} files for @implements annotations...`
    );

    for (const file of files) {
      try {
        const filePath = join(codeDirectory, file);
        const fileContent = readFileSync(filePath, 'utf-8');
        const fileAnnotations = parseAnnotations(fileContent, file);
        annotations.push(...fileAnnotations);
      } catch (error) {
        console.warn(
          `⚠️  Could not parse annotations from ${file}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    console.log(`📝 Found ${annotations.length} @implements annotations`);
    return annotations;
  } catch (error) {
    console.warn(
      `⚠️  Error scanning directory for annotations: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return annotations;
  }
}

async function main() {
  try {
    // Filter out npm/pnpm artifacts like "--"
    const args = process.argv.slice(2).filter((arg) => arg !== '--');

    const { values, positionals } = parseArgs({
      args,
      options: {
        'dry-run': {
          type: 'boolean',
          default: false,
        },
        code: {
          type: 'string',
        },
        languages: {
          type: 'string',
          default: 'py,js',
        },
        help: {
          type: 'boolean',
          default: false,
        },
      },
      allowPositionals: true,
    }) as { values: CliArgs; positionals: string[] };

    if (values.help) {
      showHelp();
      process.exit(0);
    }

    const directory = positionals[0];
    if (!directory) {
      console.error('Error: Directory argument is required');
      showHelp();
      process.exit(1);
    }

    const isDryRun = values['dry-run'] || false;
    const codeDirectory = values.code;
    const languages = values.languages
      ? values.languages.split(',')
      : ['py', 'js', 'ts'];

    console.log(`🔗 Building knowledge graph from: ${directory}`);
    if (codeDirectory) {
      console.log(`📁 Including source code from: ${codeDirectory}`);
      console.log(`🔤 Languages: ${languages.join(', ')}`);
    }
    if (isDryRun) {
      console.log('📋 Dry run mode - no files will be written');
    }

    // Parse source code if --code flag is provided
    let codeParseResult: CodeParseResult | undefined = undefined;
    let callGraphResult: CallGraphResult | undefined = undefined;
    let annotations: Annotation[] = [];

    if (codeDirectory) {
      console.log(`\n🔍 Parsing source code...`);
      codeParseResult = parseCodeDirectory(codeDirectory, languages);
      callGraphResult = extractCallGraph(codeParseResult);

      console.log(`📊 Code parsing results:`);
      console.log(`  Functions found: ${codeParseResult.functions.length}`);
      console.log(`  Calls found: ${codeParseResult.calls.length}`);
      console.log(`  Call graph edges: ${callGraphResult.edges.length}`);

      if (codeParseResult.errors.length > 0) {
        console.log(`  Parse errors: ${codeParseResult.errors.length}`);
      }

      // Parse @implements annotations
      annotations = await parseAnnotationsFromDirectory(
        codeDirectory,
        languages
      );
    }

    // Build knowledge graph from MDX files and code
    const result = await buildKnowledgeGraph(directory, {
      dryRun: isDryRun,
      outputDir: './output/kg',
      codeParseResult,
      callGraphResult,
      annotations,
    });

    if (isDryRun) {
      console.log('\n📊 Summary:');
      console.log(`  Specs found: ${result.summary.specsCount}`);
      console.log(`  Milestones: ${result.summary.milestonesCount}`);
      console.log(`  Components: ${result.summary.componentsCount}`);
      console.log(`  Relationships: ${result.summary.relationshipsCount}`);

      if (codeParseResult) {
        console.log(`  Functions added: ${codeParseResult.functions.length}`);
        console.log(
          `  workflow_calls added: ${callGraphResult?.edges.length || 0}`
        );
        console.log(
          `  Files scanned: ${callGraphResult?.stats.functionsFound || 0}`
        );
        console.log(
          `  Skipped (parse error): ${codeParseResult.errors.length}`
        );
      }

      if (result.errors.length > 0) {
        console.log(`\n⚠️  Errors: ${result.errors.length}`);
        result.errors.forEach((error) => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }

      if (codeParseResult && codeParseResult.errors.length > 0) {
        console.log(
          `\n⚠️  Code parse errors: ${codeParseResult.errors.length}`
        );
        codeParseResult.errors.forEach((error) => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    } else {
      console.log(`\n✅ Knowledge graph built successfully!`);
      console.log(`  📄 JSON-LD: ${result.files.jsonld}`);
      console.log(`  📄 YAML: ${result.files.yaml}`);
      console.log(`  📊 Specs processed: ${result.summary.specsCount}`);

      if (codeParseResult) {
        console.log(
          `  🔍 Functions added: ${codeParseResult.functions.length}`
        );
        console.log(
          `  🔗 Call edges added: ${callGraphResult?.edges.length || 0}`
        );
      }

      if (result.errors.length > 0) {
        console.log(`\n⚠️  Warnings: ${result.errors.length}`);
        result.errors.forEach((error) => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    }

    // Exit with error code if there were parsing errors
    if (
      result.errors.length > 0 ||
      (codeParseResult && codeParseResult.errors.length > 0)
    ) {
      process.exit(1);
    }
  } catch (error) {
    console.error(
      '❌ Error:',
      error instanceof Error ? error.message : String(error)
    );
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
