export default {
  testEnvironment: 'node',
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
    '^@workflow-mapper/spec-parser-lib$':
      '<rootDir>/../spec-parser-lib/src/index.ts',
    '^@workflow-mapper/code-parser-lib$':
      '<rootDir>/../code-parser-lib/src/index.ts',
    '^@workflow-mapper/kg-sync-lib$': '<rootDir>/../kg-sync-lib/src/index.ts',
    '^@workflow-mapper/shared$': '<rootDir>/../shared/src/index.ts',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(@workflow-mapper|gray-matter|yaml|remark|unified|vfile|unist|mdast|micromark)/)',
  ],
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/build-kg.ts', // Exclude CLI file with import.meta
    '!src/sync-kg.ts', // Exclude CLI file with import.meta
    '!src/audit-kg.ts', // Exclude CLI file - designed to be executed, not imported
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
