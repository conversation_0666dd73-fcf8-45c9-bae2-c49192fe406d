# Augment Ignore Configuration
# This file controls what directories and files Augment should exclude from indexing

# Work Log Directory
# Contains milestone execution artifacts and temporary work files
work-log/

# Node Modules (if not already excluded by default)
node_modules/
**/node_modules/

# Build Artifacts
build/
dist/
**/build/
**/dist/

# Cache Directories
.cache/
.turbo/
.vitest/
jest-cache/

# Knowledge Graph Generated Files
code/output/
kg.jsonld
kg.yaml
kg-changes.json
kg-viewer.html
kg-*.json
kg-*.yaml
kg-*.html

# Temporary Files
tmp/
temp/
*.tmp
*.temp

# IDE and Editor Files
.vscode/
.idea/
.obsidian/

# OS Generated Files
.DS_Store
.DS_Store?
._*
Thumbs.db

# Database Files
neo4j/
*.db
*.sqlite
*.sqlite3

# Trash Directory
trash/
