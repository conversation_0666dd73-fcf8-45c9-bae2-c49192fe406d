name: Code Parse

on:
  push:
    branches: [ main, milestone-* ]
  pull_request:
    branches: [ main ]

jobs:
  code-parse:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.0'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.4
          
      - name: Enable corepack
        run: corepack enable
        
      - name: Install dependencies
        run: |
          cd code
          pnpm install
          
      - name: Build packages
        run: |
          cd code
          pnpm run build
          
      - name: Run code parser tests
        run: |
          cd code
          pnpm --filter code-parser-lib test
          
      - name: Test CLI dry-run on fixtures
        run: |
          cd code
          pnpm run build-kg -- --code packages/code-parser-lib/tests/fixtures --dry-run .
          
      - name: Validate parsing results
        run: |
          cd code
          # Test that CLI exits with 0 and produces expected output
          OUTPUT=$(pnpm run build-kg -- --code packages/code-parser-lib/tests/fixtures --dry-run . 2>&1)
          echo "$OUTPUT"
          
          # Check for expected function count (should be >= 1)
          if echo "$OUTPUT" | grep -q "Functions added: [1-9][0-9]*"; then
            echo "✅ Functions found and parsed successfully"
          else
            echo "❌ No functions found in parsing"
            exit 1
          fi
          
          # Check for expected workflow calls (should be >= 1)  
          if echo "$OUTPUT" | grep -q "workflow_calls added: [1-9][0-9]*"; then
            echo "✅ Workflow calls found and parsed successfully"
          else
            echo "❌ No workflow calls found in parsing"
            exit 1
          fi
          
          # Check for no parse errors
          if echo "$OUTPUT" | grep -q "Skipped (parse error): 0"; then
            echo "✅ No parse errors detected"
          else
            echo "❌ Parse errors detected"
            exit 1
          fi
          
          echo "🎉 All code parsing validation checks passed!"
