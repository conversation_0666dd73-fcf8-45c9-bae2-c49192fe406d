name: <PERSON><PERSON> Sync (PR)
on: pull_request

jobs:
  sync-diff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }     # need full history for diff
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - name: Enable corepack and install dependencies
        run: corepack enable && cd code && pnpm install
      - name: Build packages
        run: cd code && pnpm build
      - name: Run incremental sync validation (dry-run)
        run: cd code && pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs
      - name: Validate coverage thresholds
        run: |
          # Final validation - fails if coverage < 0.5 or parse errors
          cd code && pnpm run sync-kg -- --since origin/main ../docs/tech-specs
# Job fails if CLI exits non-zero (coverage breach or parse error).
