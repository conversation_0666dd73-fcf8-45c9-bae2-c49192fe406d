#!/bin/sh
#
# Git commit-msg hook to enforce semantic versioning commit messages
# Platform and language agnostic solution
#

commit_file="$1"
commit_message=$(cat "$commit_file")

# Remove any leading/trailing whitespace
commit_message=$(echo "$commit_message" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# Check if commit message is empty
if [ -z "$commit_message" ]; then
    echo "❌ Commit message cannot be empty!"
    exit 1
fi

# Define valid commit types
valid_types="feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert"

# Check if commit message follows semantic format
if ! echo "$commit_message" | grep -Eq "^($valid_types)(\([a-zA-Z0-9._-]+\))?: .+$"; then
    echo "❌ Invalid commit message format!"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages must follow this format:"
    echo "   type(scope): description"
    echo ""
    echo "Valid types:"
    echo "   • feat     : A new feature"
    echo "   • fix      : A bug fix"
    echo "   • docs     : Documentation changes"
    echo "   • style    : Code style changes (formatting, etc)"
    echo "   • refactor : Code refactoring"
    echo "   • perf     : Performance improvements"
    echo "   • test     : Adding or fixing tests"
    echo "   • build    : Build system changes"
    echo "   • ci       : CI configuration changes"
    echo "   • chore    : Other changes"
    echo "   • revert   : Revert a previous commit"
    echo ""
    echo "Scope is optional. Description is required."
    echo "Scope can contain: letters, numbers, periods, underscores, hyphens"
    echo ""
    echo "Examples of good commit messages:"
    echo "   • feat(auth): add user authentication"
    echo "   • fix(M1.2): resolve parsing issue"
    echo "   • docs(user_guide): update installation guide"
    echo "   • refactor(api-service): simplify user service"
    echo "   • feat(kg-sync_lib): add annotation parser"
    echo ""
    exit 1
fi

# Check if commit message is too long (max 500 characters)
message_length=$(echo "$commit_message" | wc -c)
if [ "$message_length" -gt 500 ]; then
    echo "❌ Commit message is too long!"
    echo "📏 Current length: $message_length characters (max: 500)"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages should be:"
    echo "   • Maximum 500 characters"
    echo "   • Clear and concise"
    echo "   • Follow the format: type(scope): description"
    echo ""
    exit 1
fi

echo "✅ Commit message follows semantic convention and looks good! ($message_length chars)"
exit 0
