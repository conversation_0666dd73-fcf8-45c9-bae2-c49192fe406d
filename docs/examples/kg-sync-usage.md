# Knowledge Graph Sync Usage Examples

This document provides comprehensive examples for using the kg-sync functionality in various scenarios.

## Basic Usage Examples

### 1. Simple Incremental Sync

```bash
# Sync changes since last commit
pnpm run sync-kg -- --since HEAD~1 docs/tech-specs

# Sync changes since main branch
pnpm run sync-kg -- --since origin/main docs/tech-specs

# Sync with verbose output
pnpm run sync-kg -- --since HEAD~5 --verbose docs/tech-specs
```

### 2. Dry-Run Validation

```bash
# Validate without making changes
pnpm run sync-kg -- --since origin/main --dry-run docs/tech-specs

# Check what would be updated
pnpm run sync-kg -- --since HEAD~3 --dry-run --verbose docs/tech-specs
```

### 3. Coverage Threshold Validation

```bash
# Use default 50% threshold
pnpm run sync-kg -- --since origin/main docs/tech-specs

# Custom 70% threshold
pnpm run sync-kg -- --since origin/main --threshold 0.7 docs/tech-specs

# Strict 90% threshold
pnpm run sync-kg -- --since origin/main --threshold 0.9 docs/tech-specs
```

## Annotation Examples

### TypeScript/JavaScript Annotations

```typescript
// Basic function annotation
/**
 * @implements milestone-M1.2#AnnotationParser
 */
export function parseAnnotations(content: string, filePath: string): Annotation[] {
  // Parse JSDoc annotations from source code
  return annotations;
}

// Class annotation
/**
 * @implements milestone-M1.2#GitDiffDetector
 */
export class GitDiffDetector {
  private git: SimpleGit;

  constructor(repoPath: string) {
    this.git = simpleGit(repoPath);
  }

  /**
   * @implements milestone-M1.2#DiffAnalysis
   */
  async detectChanges(since: string): Promise<DiffResult> {
    const diff = await this.git.diff(['--name-only', since]);
    return {
      files: diff.split('\n').filter(Boolean),
      timestamp: new Date().toISOString()
    };
  }
}

// Method annotation in interface implementation
/**
 * @implements milestone-M1.2#SyncOrchestrator
 */
export class SyncOrchestrator implements ISyncOrchestrator {
  /**
   * @implements milestone-M1.2#SyncExecution
   */
  async execute(options: SyncOptions): Promise<SyncResult> {
    // Main sync logic
    return result;
  }

  /**
   * @implements milestone-M1.2#ErrorHandling
   */
  private handleError(error: Error): ParseError {
    return {
      message: error.message,
      file: 'unknown',
      line: 0,
      type: 'sync_error'
    };
  }
}
```

### Python Annotations

```python
def parse_annotations(content: str, file_path: str) -> List[Annotation]:
    """
    @implements milestone-M1.2#PythonAnnotationParser
    
    Parse annotations from Python source code.
    """
    annotations = []
    # Implementation
    return annotations

class GitDiffDetector:
    """
    @implements milestone-M1.2#PythonGitDiff
    
    Git diff detection for Python projects.
    """
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
    
    def detect_changes(self, since: str) -> Dict[str, Any]:
        """
        @implements milestone-M1.2#PythonDiffAnalysis
        
        Detect changed files since specified commit.
        """
        # Implementation
        return {"files": [], "timestamp": "2025-06-01T00:00:00Z"}
```

## Advanced Workflows

### 1. Multi-Branch Development

```bash
# Check coverage on feature branch
git checkout feature/new-component
pnpm run sync-kg -- --since main --dry-run docs/tech-specs

# Validate before merge
git checkout main
git merge --no-commit feature/new-component
pnpm run sync-kg -- --since HEAD~1 --threshold 0.8 docs/tech-specs
```

### 2. Release Validation

```bash
# Pre-release validation
pnpm run sync-kg -- --since v1.1.0 --threshold 0.9 docs/tech-specs

# Generate release notes with coverage
pnpm run sync-kg -- --since v1.1.0 --verbose docs/tech-specs > release-coverage.txt
```

### 3. Continuous Integration

```yaml
# .github/workflows/kg-sync.yml
name: Knowledge Graph Sync
on: 
  pull_request:
    paths: ['code/**', 'docs/tech-specs/**']

jobs:
  sync-validation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }
      
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      
      - name: Install and build
        run: |
          corepack enable
          cd code && pnpm install && pnpm build
      
      - name: Validate incremental sync
        run: |
          cd code
          pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs
      
      - name: Check coverage thresholds
        run: |
          cd code
          pnpm run sync-kg -- --since origin/main --threshold 0.6 ../docs/tech-specs
```

### 4. Development Hooks

```bash
# Pre-commit hook
#!/bin/bash
# .git/hooks/pre-commit
cd code
pnpm run sync-kg -- --since HEAD~1 --dry-run ../docs/tech-specs
if [ $? -ne 0 ]; then
  echo "❌ Knowledge graph sync validation failed"
  exit 1
fi
echo "✅ Knowledge graph sync validation passed"
```

## Error Handling Examples

### 1. Parse Error Recovery

```typescript
// Malformed annotation (will generate parse error)
/**
 * @implements invalid-milestone-format
 */
function badAnnotation() {
  // This will be reported as a parse error
}

// Correct annotation
/**
 * @implements milestone-M1.2#ValidComponent
 */
function goodAnnotation() {
  // This will be parsed successfully
}
```

### 2. Coverage Threshold Handling

```bash
# This will exit with code 60 if coverage < 50%
pnpm run sync-kg -- --since origin/main docs/tech-specs
echo "Exit code: $?"

# Handle different exit codes in scripts
if pnpm run sync-kg -- --since origin/main docs/tech-specs; then
  echo "✅ Sync successful"
elif [ $? -eq 60 ]; then
  echo "⚠️  Coverage below threshold"
elif [ $? -eq 70 ]; then
  echo "❌ Parse errors found"
else
  echo "❌ General error"
fi
```

### 3. Git Reference Validation

```bash
# Valid git references
pnpm run sync-kg -- --since HEAD~1 docs/tech-specs
pnpm run sync-kg -- --since origin/main docs/tech-specs
pnpm run sync-kg -- --since v1.0.0 docs/tech-specs
pnpm run sync-kg -- --since abc123def docs/tech-specs

# Invalid reference (will exit with error)
pnpm run sync-kg -- --since nonexistent-branch docs/tech-specs
```

## Performance Optimization

### 1. Large Repository Handling

```bash
# For repositories with many files, use specific commit ranges
pnpm run sync-kg -- --since HEAD~1 docs/tech-specs  # Single commit
pnpm run sync-kg -- --since HEAD~5 docs/tech-specs  # Last 5 commits

# Avoid very large diffs
# Instead of: --since origin/main (if very old)
# Use: --since HEAD~10 (more recent)
```

### 2. Selective Processing

```bash
# Process only recent changes for faster feedback
pnpm run sync-kg -- --since HEAD~1 --dry-run docs/tech-specs

# Full validation less frequently
pnpm run sync-kg -- --since origin/main docs/tech-specs
```

### 3. Monitoring Performance

```bash
# Enable verbose output to see performance metrics
pnpm run sync-kg -- --since HEAD~5 --verbose docs/tech-specs

# Example output:
# Performance metrics:
#   Files processed: 12
#   Annotations found: 45
#   Processing time: 234ms
#   Memory usage: 15.2MB
```

## Troubleshooting

### Common Issues

1. **Git Reference Not Found**
   ```bash
   # Error: Git reference 'nonexistent' not found
   # Solution: Use valid git reference
   git branch -a  # List available branches
   git log --oneline -10  # List recent commits
   ```

2. **Parse Errors in Annotations**
   ```bash
   # Error: Invalid milestone format
   # Solution: Fix annotation format
   # Wrong: @implements invalid-format
   # Right: @implements milestone-M1.2#ComponentName
   ```

3. **Coverage Threshold Breach**
   ```bash
   # Error: Coverage 45% below threshold 50%
   # Solution: Add more annotations or lower threshold
   pnpm run sync-kg -- --since origin/main --threshold 0.4 docs/tech-specs
   ```

4. **File System Permissions**
   ```bash
   # Error: Cannot write to output directory
   # Solution: Check permissions or specify different output directory
   pnpm run sync-kg -- --since HEAD~1 --output-dir ./tmp docs/tech-specs
   ```

### Debug Mode

```bash
# Enable debug logging
KG_SYNC_DEBUG=1 pnpm run sync-kg -- --since HEAD~1 --verbose docs/tech-specs

# This will show:
# - Git diff command execution
# - File processing details
# - Annotation parsing steps
# - Graph update operations
# - Performance timing
```

## Best Practices

1. **Annotation Placement**: Always place annotations directly above the function/class they describe
2. **Consistent Naming**: Use consistent component names across specifications and annotations
3. **Regular Validation**: Run sync validation frequently during development
4. **Threshold Management**: Set appropriate coverage thresholds for different environments
5. **Git Hygiene**: Use meaningful commit messages and regular commits for better diff tracking
6. **Documentation**: Keep annotations up-to-date when refactoring code
7. **CI Integration**: Include sync validation in your CI/CD pipeline
8. **Performance Monitoring**: Monitor sync performance and adjust commit ranges as needed
