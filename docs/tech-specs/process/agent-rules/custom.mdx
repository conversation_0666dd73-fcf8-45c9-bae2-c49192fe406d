---
title: "Custom Agent Configuration Template"
description: "Template for configuring custom AI agents for milestone implementation"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "custom", "template", "executable"]
authors: ["nitishMehrotra"]
---

# Custom Agent Configuration Template

> **🎯 Purpose:** Template for configuring custom AI agents. For universal rules, see [Core Agent Rules](./core.mdx).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Custom Agent Best Practices
- **Agent-Specific Capabilities**: Leverage unique features of your chosen agent
- **Configuration Management**: Maintain agent-specific configuration files
- **Validation Integration**: Ensure agent can execute validation commands
- **Process Adherence**: Follow core rules regardless of agent type

### Essential Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh

# Validate documentation structure
node docs/scripts/validate-structure.mjs
```

---

## 🚀 QUICK SETUP

### Universal Configuration Template
Adapt this template for your specific AI system:

```markdown
Agent Name: {YOUR_AGENT_NAME}
Milestone: {MILESTONE_ID} - {MILESTONE_TITLE}

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md BEFORE starting
- Update work logs in real-time (max 15min lag)
- Validate ALL success criteria immediately after implementation
- Use package managers ONLY (never edit package files manually)
- Run bash docs/scripts/acceptance/{milestone_script}-acceptance.sh immediately after completion

QUALITY GATES:
- Implement ALL specified success criteria (no skipping)
- Achieve minimum test coverage thresholds: Unit (80%), Integration (70%), E2E (60%)
- Pass all linting and type checking
- Include comprehensive error handling

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

ERROR RECOVERY REQUIREMENTS:
- Stop implementation when critical errors detected
- Document all errors and resolution attempts
- Seek human intervention for unresolvable issues
- Maintain rollback capability at all times
- Follow error recovery procedures from error-recovery.mdx

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Error handling: docs/tech-specs/process/core/error-recovery.mdx

PLACEHOLDERS TO REPLACE:
- {YOUR_AGENT_NAME} → Your AI system name
- {MILESTONE_ID} → Your milestone ID (e.g., "M1")
- {MILESTONE_TITLE} → Your milestone title
- {milestone_id} → Lowercase milestone ID (e.g., "m1")
- {milestone_script} → Acceptance script name (e.g., "m1")
```

---

## 🔧 SYSTEM-SPECIFIC ADAPTATIONS

### For File-Based Configuration Systems
```yaml
# config/agent-rules.yaml
agent:
  name: "{YOUR_AGENT_NAME}"
  milestone: "{MILESTONE_ID}"
rules:
  core_reference: "docs/tech-specs/process/agent-rules/core.mdx"
  mandatory_actions:
    - "Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting"
    - "Update work logs in real-time during implementation"
    - "Validate ALL success criteria immediately after implementation"
    - "Use package managers instead of manual file editing"
    - "Run acceptance tests immediately after completion"
```

### For API-Based Systems
```json
{
  "agent_configuration": {
    "name": "{YOUR_AGENT_NAME}",
    "milestone": "{MILESTONE_ID}",
    "core_rules": "docs/tech-specs/process/agent-rules/core.mdx",
    "quality_gates": {
      "test_coverage_minimum": 80,
      "documentation_sync_max_lag": "15 minutes",
      "acceptance_test_required": true,
      "package_manager_only": true
    }
  }
}
```

### For Prompt-Based Systems
```markdown
You are implementing Milestone {MILESTONE_ID}: {MILESTONE_TITLE} using {YOUR_AGENT_NAME}.

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- Update work logs in real-time during implementation
- Validate each success criterion immediately after implementation
- Use package managers instead of manual file editing
- Run bash docs/scripts/acceptance/{milestone_script}-acceptance.sh after completion
```

## 📋 QUALITY REQUIREMENTS

### Test Coverage Standards
- **Unit Tests**: 80% minimum coverage for all new functionality
- **Integration Tests**: 70% minimum coverage for component interactions
- **End-to-End Tests**: 60% minimum coverage for critical user workflows

### Code Quality Standards
- **ESLint Score**: >8.0 with zero errors, warnings acceptable
- **Complexity**: Cyclomatic complexity <10 per function
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities from security scans

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level (P0-P3)
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity level
5. **Begin investigation** and document in tracking system

### Error Severity Classification
- **Critical (P0)**: System down, data loss, security breach → **15 minutes response**
- **High (P1)**: Major functionality broken, >50% performance degradation → **1 hour response**
- **Medium (P2)**: Minor issues, 10-50% performance impact → **4 hours response**
- **Low (P3)**: Cosmetic issues, documentation errors → **24 hours response**

---

## ✅ VALIDATION CHECKLIST

### Agent Capability Assessment
Before implementing, assess your AI system's capabilities:

#### Code Generation
- [ ] Can generate code from specifications
- [ ] Understands multiple programming languages
- [ ] Follows coding conventions and patterns
- [ ] Generates appropriate tests
- [ ] Includes error handling

#### Process Following
- [ ] Can follow multi-step processes
- [ ] Remembers previous steps and context
- [ ] Validates requirements systematically
- [ ] Tracks progress against checklists
- [ ] Escalates when encountering issues

### Configuration Testing
Test your custom configuration with these steps:

#### Small Test Implementation
1. **Choose simple task** from milestone requirements
2. **Configure your AI system** with adapted template
3. **Execute the task** following all process requirements
4. **Evaluate results** against quality standards
5. **Refine configuration** based on outcomes

#### Process Compliance Check
- [ ] AI creates work log before starting
- [ ] Documentation updated in real-time
- [ ] Tests generated and executed
- [ ] Acceptance criteria validated
- [ ] Quality standards met

## 🚨 TROUBLESHOOTING

### Capability Gaps & Workarounds
If your AI system lacks certain capabilities:

**Limited Code Generation**:
- Provide more detailed specifications
- Use human review for complex implementations
- Focus AI on simpler, well-defined tasks

**Poor Documentation**:
- Use templates for consistent structure
- Implement human review for documentation
- Use separate tools for documentation generation

**Weak Process Following**:
- Break down processes into smaller steps
- Use external tracking systems
- Implement human oversight for process compliance

### Iterative Improvement
1. **Identify gaps** in AI system performance
2. **Adjust configuration** to address weaknesses
3. **Test improvements** with additional tasks
4. **Document lessons learned** for future use
5. **Share insights** with team for broader benefit

## 🔗 PROCESS REFERENCES

### Core Rules
This template extends: [`core.mdx`](./core.mdx)

### Detailed Processes
- **Milestone Implementation**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Error Recovery**: [`../core/error-recovery.mdx`](../core/error-recovery.mdx)

### Example Configurations
Reference these for examples:
- **Claude**: [`claude.mdx`](./claude.mdx)
- **Copilot**: [`copilot.mdx`](./copilot.mdx)
- **Cursor**: [`cursor.mdx`](./cursor.mdx)

### Templates
- **Work Log**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)
- **Validation**: [`validation.mdx`](./validation.mdx)

---

**Template Owner**: AI Configuration Team
**Review Frequency**: When new AI systems are adopted
**Last Updated**: 2025-05-25
