---
title: "Augment Agent Configuration"
description: "Augment Agent specific configuration for milestone implementation"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "augment", "executable"]
authors: ["nitishMehrotra"]
---

# Augment Agent Configuration

> **🎯 Purpose:** Augment Agent specific configuration templates. For universal rules, see [Core Agent Rules](./core.mdx).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Augment-Specific Tools
- **codebase-retrieval**: Understand existing code patterns and context
- **view**: Comprehensive file analysis (use large ranges 500+ lines)
- **str-replace-editor**: Precise code modifications with exact matching
- **launch-process**: Testing, validation, and command execution
- **diagnostics**: Proactive issue detection and error catching

### Essential Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh

# Validate documentation structure
node docs/scripts/validate-structure.mjs
```

---

## 🚀 QUICK SETUP

### Memory Configuration
Use the `remember` tool to store milestone-specific context:

```markdown
Remember: Implementing Milestone {MILESTONE_ID}: {MILESTONE_TITLE}

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md BEFORE starting
- Update work logs in real-time during implementation (max 15min lag)
- Validate ALL success criteria immediately after implementation
- Use package managers ONLY (never edit package files manually)
- Run bash docs/scripts/acceptance/{milestone_script}-acceptance.sh immediately after completion

QUALITY GATES:
- Implement ALL specified success criteria (no skipping)
- Achieve minimum test coverage thresholds: Unit (80%), Integration (70%), E2E (60%)
- Pass all linting and type checking
- Include comprehensive error handling

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

AUGMENT-SPECIFIC OPTIMIZATIONS:
- Use codebase-retrieval for understanding existing code patterns
- Leverage view tool for comprehensive file analysis
- Use str-replace-editor for precise code modifications
- Apply launch-process for testing and validation
- Use diagnostics to catch issues early

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Error handling: docs/tech-specs/process/core/error-recovery.mdx

PLACEHOLDERS TO REPLACE:
- {MILESTONE_ID} → Your milestone ID (e.g., "M1")
- {MILESTONE_TITLE} → Your milestone title
- {milestone_id} → Lowercase milestone ID (e.g., "m1")
- {milestone_script} → Acceptance script name (e.g., "m1")
```

## 🤖 AUGMENT-SPECIFIC OPTIMIZATIONS

### Leverage Augment's Unique Capabilities

#### **1. Codebase Understanding**
```markdown
# Before making changes, always understand the context
Use codebase-retrieval to:
- Understand existing code patterns and conventions
- Find related functionality and integration points
- Identify architectural decisions and constraints
- Locate relevant tests and documentation

Example: "Find all existing utility functions and their patterns to ensure consistency with new implementation"
```

#### **2. Comprehensive Analysis**
```markdown
# Use view tool for thorough analysis
- View directory structures to understand organization
- Examine existing files for patterns and conventions
- Check related files for integration requirements
- Analyze test files for testing patterns

Example: view entire files with large ranges (500+ lines) for efficiency
```

#### **3. Precise Modifications**
```markdown
# Use str-replace-editor for surgical changes
- Make targeted edits without disrupting existing code
- Preserve formatting and style consistency
- Update multiple files systematically
- Maintain backward compatibility

Always use instruction_reminder: "ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 200 LINES EACH."
```

#### **4. Validation & Testing**
```markdown
# Use launch-process for comprehensive validation
- Run tests immediately after implementation
- Execute linting and type checking
- Validate acceptance criteria with scripts
- Test integration points thoroughly

Use diagnostics tool to catch issues early in development
```

### Augment Workflow Optimization

#### **Phase 1: Analysis & Planning**
1. **Use codebase-retrieval** to understand requirements and context
2. **Use view** to examine milestone specification thoroughly
3. **Create requirement checklist** using templates
4. **Plan implementation approach** based on existing patterns

#### **Phase 2: Implementation**
1. **Use codebase-retrieval** to find relevant code patterns
2. **Use view** to examine files before modification
3. **Use str-replace-editor** for precise code changes
4. **Update work log** in real-time during implementation

#### **Phase 3: Validation**
1. **Use launch-process** to run tests and validation
2. **Use diagnostics** to check for issues
3. **Execute acceptance tests** with launch-process
4. **Complete work log** with comprehensive results

## 📋 QUALITY REQUIREMENTS

### Test Coverage Standards
- **Unit Tests**: 80% minimum coverage for all new functionality
- **Integration Tests**: 70% minimum coverage for component interactions
- **End-to-End Tests**: 60% minimum coverage for critical user workflows
- **Test Quality**: Meaningful assertions, edge cases covered, no flaky tests

### Code Quality Standards
- **ESLint Score**: >8.0 with zero errors, warnings acceptable
- **Complexity**: Cyclomatic complexity <10 per function
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities from security scans
- **Documentation**: 100% API coverage, all public functions documented

### Documentation Requirements
- **Real-time sync**: Max 15-minute lag between implementation and documentation
- **Template compliance**: Use appropriate templates, replace ALL placeholders
- **Cross-references**: Maintain working links and consistent structure
- **Validation**: All documentation must pass spec-lint.mjs validation

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level (P0-P3)
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity level
5. **Begin investigation** and document in tracking system

### Error Severity Classification
- **Critical (P0)**: System down, data loss, security breach → **15 minutes response**
- **High (P1)**: Major functionality broken, >50% performance degradation → **1 hour response**
- **Medium (P2)**: Minor issues, 10-50% performance impact → **4 hours response**
- **Low (P3)**: Cosmetic issues, documentation errors → **24 hours response**

### Rollback Decision Criteria
Execute rollback when:
- **Critical system failure** cannot be quickly fixed
- **Data integrity** is at risk
- **Security vulnerability** is actively exploited
- **Fix timeline** exceeds acceptable downtime

### Recovery Commands
```bash
# Git-based rollback to previous stable commit
git checkout main && git reset --hard <previous-stable-commit>
git push --force-with-lease origin main

# Database rollback and restore
npm run migrate:rollback
pg_restore --clean --if-exists -d database_name backup_file.sql
```

---

## ✅ VALIDATION CHECKLIST

### Pre-Implementation
- [ ] Used codebase-retrieval to understand milestone requirements
- [ ] Examined existing code patterns with view tool
- [ ] Created requirement checklist using template
- [ ] Planned implementation approach systematically
- [ ] Configured Augment with milestone-specific memory

### During Implementation
- [ ] Using codebase-retrieval for context understanding
- [ ] Using view tool for comprehensive file analysis
- [ ] Using str-replace-editor for precise modifications
- [ ] Updating work log in real-time (max 15min lag)
- [ ] Following systematic implementation approach

### Post-Implementation
- [ ] Used launch-process to run all tests
- [ ] Used diagnostics to check for issues
- [ ] Executed acceptance tests successfully
- [ ] Completed comprehensive work log
- [ ] All success criteria validated

## 🚨 TROUBLESHOOTING

### Common Augment Issues
**Issue**: Codebase-retrieval returns too much irrelevant information
**Solution**: Use more specific queries focusing on exact functionality needed

**Issue**: View tool output is truncated for large files
**Solution**: Use view_range parameter to examine specific sections systematically

**Issue**: str-replace-editor fails due to whitespace issues
**Solution**: Copy exact text including whitespace, use smaller chunks

**Issue**: Launch-process commands fail or timeout
**Solution**: Check working directory, use appropriate wait times, verify commands

### Augment-Specific Best Practices
- **Information Gathering**: Always use codebase-retrieval before making changes
- **File Analysis**: Use view with large ranges (500+ lines) for efficiency
- **Precise Editing**: Use str-replace-editor with exact text matching
- **Systematic Validation**: Use launch-process for comprehensive testing
- **Issue Detection**: Use diagnostics proactively to catch problems early

## 🔗 PROCESS REFERENCES

### Core Rules
This configuration extends: [`core.mdx`](./core.mdx)

### Detailed Processes
- **Milestone Implementation**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Git Workflow**: [`../core/git-workflow.mdx`](../core/git-workflow.mdx)
- **Error Recovery**: [`../core/error-recovery.mdx`](../core/error-recovery.mdx)

### Templates
- **Work Log**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)
- **Validation**: [`validation.mdx`](./validation.mdx)

### Other Agent Configurations
- **Claude**: [`claude.mdx`](./claude.mdx)
- **GitHub Copilot**: [`copilot.mdx`](./copilot.mdx)
- **Cursor**: [`cursor.mdx`](./cursor.mdx)
- **Custom**: [`custom.mdx`](./custom.mdx)

---

**Configuration Owner**: Augment Agent Users
**Review Frequency**: After each milestone or when issues identified
**Last Updated**: 2025-05-25
