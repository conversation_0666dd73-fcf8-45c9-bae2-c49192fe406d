---
title: "Cursor Agent Configuration"
description: "Cursor AI specific configuration for milestone implementation"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "cursor", "executable"]
authors: ["nitishMehrotra"]
---

# Cursor Agent Configuration

> **🎯 Purpose:** Cursor AI specific configuration templates. For universal rules, see [Core Agent Rules](./core.mdx).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Cursor-Specific Best Practices
- **Codebase Context**: Leverage Cursor's full codebase understanding
- **Multi-file Editing**: Use Cursor's ability to edit multiple files simultaneously
- **AI-Powered Refactoring**: Utilize Cursor's advanced refactoring capabilities
- **Integrated Terminal**: Use built-in terminal for testing and validation

### Essential Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh

# Validate documentation structure
node docs/scripts/validate-structure.mjs
```

---

## 🚀 QUICK SETUP

### Create `.cursor/rules` File
Create this file in your project root:

```markdown
# Cursor Rules for Milestone {MILESTONE_ID}: {MILESTONE_TITLE}

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- Update work logs in real-time during implementation (max 15min lag)
- Validate ALL success criteria immediately after implementation
- Use package managers ONLY (never edit package files manually)
- Run bash docs/scripts/acceptance/{milestone_script}-acceptance.sh immediately after completion

QUALITY GATES:
- Implement ALL specified success criteria (no skipping)
- Achieve minimum test coverage thresholds: Unit (80%), Integration (70%), E2E (60%)
- Pass all linting and type checking
- Include comprehensive error handling

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Error handling: docs/tech-specs/process/core/error-recovery.mdx

PLACEHOLDERS TO REPLACE:
- {MILESTONE_ID} → Your milestone ID (e.g., "M1")
- {MILESTONE_TITLE} → Your milestone title
- {milestone_id} → Lowercase milestone ID (e.g., "m1")
- {milestone_script} → Acceptance script name (e.g., "m1")
```

---

## 🤖 CURSOR-SPECIFIC OPTIMIZATIONS

### Cursor Chat Prompts
Use these structured prompts in Cursor chat:

```markdown
# Project Setup
"Set up milestone {MILESTONE_ID} implementation following these steps:
1. Create work-log/milestone-{milestone_id}/requirement-checklist.md
2. Review all success criteria from milestone specification
3. Validate that all criteria are testable and measurable
Follow process requirements from docs/tech-specs/process/agent-rules/core.mdx"

# Feature Implementation
"Implement [specific feature] for milestone {MILESTONE_ID}:
- Follow success criteria from milestone specification
- Use TypeScript strict mode with proper types
- Include comprehensive error handling
- Add unit tests with 80% coverage minimum
- Update work log in real-time"

# Code Review
"Review current implementation for milestone {MILESTONE_ID} compliance:
- Check against success criteria
- Validate error handling patterns
- Verify test coverage requirements
- Confirm TypeScript strict mode compliance"
```

### Leveraging Cursor's Strengths
- **Codebase Understanding**: Index entire project for better context awareness
- **Natural Language Programming**: Describe requirements in natural language first
- **Context-Aware Development**: Reference milestone specifications in chat prompts
- **Iterative Implementation**: Use chat for step-by-step implementation plans

## 📋 QUALITY REQUIREMENTS

### Test Coverage Standards
- **Unit Tests**: 80% minimum coverage for all new functionality
- **Integration Tests**: 70% minimum coverage for component interactions
- **End-to-End Tests**: 60% minimum coverage for critical user workflows

### Code Quality Standards
- **ESLint Score**: >8.0 with zero errors, warnings acceptable
- **Complexity**: Cyclomatic complexity <10 per function
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities from security scans

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level (P0-P3)
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity level
5. **Begin investigation** and document in tracking system

### Error Severity Classification
- **Critical (P0)**: System down, data loss, security breach → **15 minutes response**
- **High (P1)**: Major functionality broken, >50% performance degradation → **1 hour response**
- **Medium (P2)**: Minor issues, 10-50% performance impact → **4 hours response**
- **Low (P3)**: Cosmetic issues, documentation errors → **24 hours response**

---

## ✅ VALIDATION CHECKLIST

### Pre-Implementation
- [ ] `.cursor/rules` file created with milestone-specific values
- [ ] Project context properly indexed by Cursor
- [ ] Milestone specification accessible in workspace
- [ ] Related documentation files available
- [ ] Development environment properly configured

### During Implementation
- [ ] Following rules defined in `.cursor/rules`
- [ ] Using chat effectively for complex implementations
- [ ] Validating generated code against requirements
- [ ] Maintaining real-time work log updates
- [ ] Testing incrementally during development

### Post-Implementation
- [ ] All acceptance tests passing
- [ ] Code coverage meets minimum thresholds
- [ ] Documentation updated and accurate
- [ ] Work log comprehensive and complete
- [ ] All success criteria validated

## 🚨 TROUBLESHOOTING

### Common Issues
**Issue**: Cursor generates code that doesn't follow project conventions
**Solution**: Ensure `.cursor/rules` file is comprehensive and reference existing code patterns

**Issue**: AI suggestions don't understand complex requirements
**Solution**: Break down requirements into smaller, more specific prompts

**Issue**: Generated code lacks proper error handling
**Solution**: Explicitly request error handling in prompts and provide examples

### Quick Fixes
- **Configuration**: Verify `.cursor/rules` file is in project root with correct syntax
- **Context**: Ensure project is properly indexed and relevant files are open
- **Quality**: Use specific prompts with detailed requirements and validate systematically

## 🔗 PROCESS REFERENCES

### Core Rules
This configuration extends: [`core.mdx`](./core.mdx)

### Detailed Processes
- **Milestone Implementation**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Git Workflow**: [`../core/git-workflow.mdx`](../core/git-workflow.mdx)
- **Error Recovery**: [`../core/error-recovery.mdx`](../core/error-recovery.mdx)

### Templates
- **Work Log**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)
- **Validation**: [`validation.mdx`](./validation.mdx)

---

**Configuration Owner**: Cursor Users
**Review Frequency**: After each milestone or when issues identified
**Last Updated**: 2025-05-25
