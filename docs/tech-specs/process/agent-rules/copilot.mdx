---
title: "GitHub Copilot Agent Configuration"
description: "GitHub Copilot specific configuration for milestone implementation"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "copilot", "github", "executable"]
authors: ["nitishMehrotra"]
---

# GitHub Copilot Agent Configuration

> **🎯 Purpose:** GitHub Copilot specific configuration templates. For universal rules, see [Core Agent Rules](./core.mdx).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Copilot-Specific Best Practices
- **Comment-Driven Development**: Use descriptive comments to guide code generation
- **Context Provision**: Provide sufficient context files and existing patterns
- **Review All Suggestions**: Never accept code without thorough review
- **Test Generated Code**: Add comprehensive tests for all generated functionality

### Essential Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh

# Validate documentation structure
node docs/scripts/validate-structure.mjs
```

---

## 🚀 QUICK SETUP

### VS Code Workspace Settings
Create or update `.vscode/settings.json` in your project root:

```json
{
  "github.copilot.enable": {
    "*": true,
    "yaml": true,
    "plaintext": false,
    "markdown": true
  },
  "github.copilot.chat.codeGeneration.instructions": [
    "Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx",
    "Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting",
    "Update work logs in real-time during implementation",
    "Validate ALL success criteria immediately after implementation",
    "Use package managers for dependencies, never edit package files manually",
    "Run acceptance tests immediately after completion",
    "Include comprehensive testing with appropriate coverage thresholds",
    "Follow conventional commit message format for all commits"
  ]
}
```

### Copilot Instructions File
Create `.github/copilot-instructions.md` in your project root:

```markdown
# GitHub Copilot Instructions for {MILESTONE_ID}: {MILESTONE_TITLE}

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- Update work logs in real-time during implementation
- Validate ALL success criteria immediately after implementation
- Use package managers for dependencies, never edit package files manually
- Run acceptance tests immediately after completion

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

ERROR RECOVERY REQUIREMENTS:
- Stop implementation when critical errors detected
- Document all errors and resolution attempts
- Seek human intervention for unresolvable issues
- Maintain rollback capability at all times
- Follow error recovery procedures from error-recovery.mdx

QUALITY STANDARDS:
- Use TypeScript strict mode with proper type definitions
- Implement comprehensive error handling for all functions
- Add JSDoc comments for all public APIs and complex functions
- Follow established code patterns and architectural decisions
- Achieve minimum coverage thresholds: Unit (80%), Integration (70%), E2E (60%)

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Git workflow: docs/tech-specs/process/core/git-workflow.mdx

PLACEHOLDERS TO REPLACE:
- {MILESTONE_ID} → Your milestone ID (e.g., "M1")
- {MILESTONE_TITLE} → Your milestone title
- {milestone_id} → Lowercase milestone ID (e.g., "m1")
- {milestone_script} → Acceptance script name (e.g., "m1")
```

## 🤖 COPILOT-SPECIFIC OPTIMIZATIONS

### Comment-Driven Development
Use descriptive comments to guide Copilot:

```typescript
// Create a JSON-LD serializer for workflow mappings
// Should support schema.org vocabulary
// Include custom context definitions
// Handle nested workflow structures
// Return properly formatted JSON-LD object
export class WorkflowJsonLdSerializer {
  // Implementation will be suggested by Copilot
}
```

### Copilot Chat Prompts
```markdown
# Pre-Implementation Setup
"Help me set up milestone {MILESTONE_ID} implementation:
1. Create work-log/milestone-{milestone_id}/requirement-checklist.md
2. Validate all success criteria are testable
3. Plan dependency compatibility matrix
Follow process requirements from docs/tech-specs/process/agent-rules/core.mdx"

# Implementation Request
"Implement [specific feature] following milestone {MILESTONE_ID} requirements:
- Use TypeScript strict mode with proper types
- Include comprehensive error handling
- Add unit tests with 80% coverage minimum
- Update work log in real-time"

# Code Review Request
"Review this code for milestone {MILESTONE_ID} compliance:
- Check against success criteria
- Validate error handling and test coverage
- Confirm TypeScript types and documentation"
```

### Best Practices
- **Provide context** with imports and existing patterns
- **Use descriptive comments** to guide code generation
- **Review all suggestions** before accepting
- **Test generated code** thoroughly
- **Establish consistent patterns** early in project

## 📋 QUALITY REQUIREMENTS

### Test Coverage Standards
- **Unit Tests**: 80% minimum coverage for all new functionality
- **Integration Tests**: 70% minimum coverage for component interactions
- **End-to-End Tests**: 60% minimum coverage for critical user workflows

### Code Quality Standards
- **ESLint Score**: >8.0 with zero errors, warnings acceptable
- **Complexity**: Cyclomatic complexity <10 per function
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities from security scans

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level (P0-P3)
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity level
5. **Begin investigation** and document in tracking system

### Error Severity Classification
- **Critical (P0)**: System down, data loss, security breach → **15 minutes response**
- **High (P1)**: Major functionality broken, >50% performance degradation → **1 hour response**
- **Medium (P2)**: Minor issues, 10-50% performance impact → **4 hours response**
- **Low (P3)**: Cosmetic issues, documentation errors → **24 hours response**

---

## ✅ VALIDATION CHECKLIST

### Pre-Implementation
- [ ] Workspace settings configured with milestone-specific values
- [ ] Copilot instructions file created and customized
- [ ] Project context files available for reference
- [ ] Testing frameworks and tools properly configured
- [ ] Linting and formatting tools set up

### During Implementation
- [ ] Generated code follows project patterns
- [ ] All suggestions reviewed before acceptance
- [ ] Tests generated for new functionality
- [ ] Documentation updated with generated code
- [ ] Error handling included in generated code

### Post-Implementation
- [ ] All acceptance tests pass
- [ ] Code coverage meets thresholds
- [ ] Security scans show no issues
- [ ] Performance benchmarks met
- [ ] Documentation is complete and accurate

## 🚨 TROUBLESHOOTING

### Common Issues
**Issue**: Copilot generates code that doesn't follow project patterns
**Solution**: Provide more context files and establish clear patterns early

**Issue**: Generated tests are superficial or incomplete
**Solution**: Use specific comments describing test requirements and edge cases

**Issue**: Copilot suggestions don't match TypeScript strict mode
**Solution**: Ensure TypeScript configuration is visible and use type annotations

### Quick Fixes
- **Configuration**: Verify Copilot extension enabled and workspace settings correct
- **Context**: Provide sufficient context with imports and existing patterns
- **Quality**: Review and refine generated code systematically
- **Testing**: Add comprehensive tests for all generated functionality

## 🔗 PROCESS REFERENCES

### Core Rules
This configuration extends: [`core.mdx`](./core.mdx)

### Detailed Processes
- **Milestone Implementation**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Git Workflow**: [`../core/git-workflow.mdx`](../core/git-workflow.mdx)

### Templates
- **Work Log**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)
- **Validation**: [`validation.mdx`](./validation.mdx)

---

**Configuration Owner**: GitHub Copilot Users
**Review Frequency**: After each milestone or when issues identified
**Last Updated**: 2025-05-25
