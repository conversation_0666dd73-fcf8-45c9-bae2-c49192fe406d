---
title: "Claude/Anthropic Agent Configuration"
description: "Claude/Anthropic specific configuration for milestone implementation"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "claude", "anthropic", "executable"]
authors: ["nitishMehrotra"]
---

# Claude/Anthropic Agent Configuration

> **🎯 Purpose:** Claude/Anthropic specific configuration templates. For universal rules, see [Core Agent Rules](./core.mdx).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Claude-Specific Best Practices
- **Context Management**: Provide full milestone specification in conversation
- **Structured Summaries**: Use for long implementations to maintain context
- **Explicit Validation**: Require confirmation of understanding before proceeding
- **Concrete Implementation**: Request executable code with specific file paths

### Essential Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh

# Validate documentation structure
node docs/scripts/validate-structure.mjs
```

---

## 🚀 QUICK SETUP

### System Instructions Template
Copy this configuration into Claude's system instructions:

```markdown
You are implementing Milestone {MILESTONE_ID}: {MILESTONE_TITLE}.

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-{milestone_id}/requirement-checklist.md BEFORE starting
- Update work logs in real-time (max 15min lag)
- Validate ALL success criteria immediately after implementation
- Use package managers ONLY (never edit package files manually)
- Run bash docs/scripts/acceptance/{milestone_script}-acceptance.sh immediately after completion

QUALITY GATES:
- Implement ALL specified success criteria (no skipping)
- Achieve minimum test coverage thresholds: Unit (80%), Integration (70%), E2E (60%)
- Pass all linting and type checking
- Include comprehensive error handling

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

ERROR RECOVERY REQUIREMENTS:
- Stop implementation when critical errors detected
- Document all errors and resolution attempts
- Seek human intervention for unresolvable issues
- Maintain rollback capability at all times
- Follow error recovery procedures from error-recovery.mdx

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Error handling: docs/tech-specs/process/core/error-recovery.mdx

PLACEHOLDERS TO REPLACE:
- {MILESTONE_ID} → Your milestone ID (e.g., "M1")
- {MILESTONE_TITLE} → Your milestone title
- {milestone_id} → Lowercase milestone ID (e.g., "m1")
- {milestone_script} → Acceptance script name (e.g., "m1")
```

## 🤖 CLAUDE-SPECIFIC OPTIMIZATIONS

### Context Management
- **Provide full milestone specification** in conversation context
- **Use structured summaries** for long implementations
- **Reference key decisions** regularly to maintain context
- **Update implementation state** in structured format

### Effective Prompting
```markdown
# Implementation Request Template
"Implement [specific feature] following milestone requirements. Before starting:
1. Confirm understanding of requirements
2. Outline implementation approach
3. Identify potential risks
4. Proceed only after confirmation"

# Validation Request Template
"Validate current implementation against success criteria:
1. Check each criterion systematically
2. Run all relevant tests
3. Document gaps or issues
4. Provide remediation steps if needed"
```

### Leveraging Claude's Strengths
- **Code Quality**: Request detailed reviews and multiple approaches
- **Problem Solving**: Present complex problems with full context
- **Documentation**: Use natural language for clear documentation
- **Reasoning**: Ask for step-by-step analysis and explanations

## 📋 QUALITY REQUIREMENTS

### Test Coverage Standards
- **Unit Tests**: 80% minimum coverage for all new functionality
- **Integration Tests**: 70% minimum coverage for component interactions
- **End-to-End Tests**: 60% minimum coverage for critical user workflows
- **Test Quality**: Meaningful assertions, edge cases covered, no flaky tests

### Code Quality Standards
- **ESLint Score**: >8.0 with zero errors, warnings acceptable
- **Complexity**: Cyclomatic complexity <10 per function
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities from security scans
- **Documentation**: 100% API coverage, all public functions documented

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level (P0-P3)
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity level
5. **Begin investigation** and document in tracking system

### Error Severity Classification
- **Critical (P0)**: System down, data loss, security breach → **15 minutes response**
- **High (P1)**: Major functionality broken, >50% performance degradation → **1 hour response**
- **Medium (P2)**: Minor issues, 10-50% performance impact → **4 hours response**
- **Low (P3)**: Cosmetic issues, documentation errors → **24 hours response**

---

## ✅ VALIDATION CHECKLIST

### Pre-Implementation
- [ ] System instructions configured with milestone-specific values
- [ ] Full milestone specification provided in context
- [ ] Claude confirms understanding of all requirements
- [ ] Implementation approach validated
- [ ] Risk assessment completed

### During Implementation
- [ ] Claude provides regular progress updates
- [ ] Technical decisions explained and documented
- [ ] Tests run and results validated
- [ ] Documentation updated in real-time
- [ ] Issues reported immediately

### Post-Implementation
- [ ] All acceptance tests pass
- [ ] Claude confirms all requirements met
- [ ] Documentation complete and synchronized
- [ ] Work log comprehensive and actionable
- [ ] No outstanding issues or concerns

## 🚨 TROUBLESHOOTING

### Common Issues
**Issue**: Claude provides theoretical solutions without practical implementation
**Solution**: Request concrete, executable code with specific file paths

**Issue**: Claude loses context during long implementations
**Solution**: Provide regular summaries and structured state tracking

**Issue**: Claude makes assumptions about requirements
**Solution**: Require explicit validation of understanding before proceeding

**Issue**: Claude doesn't run actual tests
**Solution**: Always execute tests yourself and provide results to Claude

### Quick Fixes
- **Configuration**: Verify system instructions and placeholders replaced
- **Context**: Ensure milestone specification is in conversation context
- **Understanding**: Validate Claude's comprehension with test questions
- **Implementation**: Combine Claude's analysis with actual testing

## 🔗 PROCESS REFERENCES

### Core Rules
This configuration extends: [`core.mdx`](./core.mdx)

### Detailed Processes
- **Milestone Implementation**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Error Recovery**: [`../core/error-recovery.mdx`](../core/error-recovery.mdx)
- **Git Workflow**: [`../core/git-workflow.mdx`](../core/git-workflow.mdx)

### Templates
- **Work Log**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)
- **Validation**: [`validation.mdx`](./validation.mdx)

---

**Configuration Owner**: Claude Users
**Review Frequency**: After each milestone or when issues identified
**Last Updated**: 2025-05-25
