---
title: ADR-XXX — <Decision Title>
description: <Brief description of the architectural decision>
created: <YYYY-MM-DD>
updated: <YYYY-MM-DD>
version: 0.1.0
status: Proposed
tags: [adr, architecture]
authors: [<author>]
---



<Callout emoji="🏗️">
<strong>Architectural Decision Record.</strong> This document captures an important architectural decision made for the WorkflowMapperAgent project.
</Callout>

> **📋 Process Guidelines:** For complete ADR creation, review, and management processes, see [Core Process Guidelines](../agent-rules/core.mdx#🏗️-architectural-decision-process).

---

## 📋 Decision Summary

**ID**: ADR-XXX
**Date**: <YYYY-MM-DD>
**Status**: Proposed | Accepted | Deprecated | Superseded
**Deciders**: <List of people involved in the decision>
**Technical Story**: <Link to related issue/story if applicable>

---

## 🎯 Context and Problem Statement

<Describe the context and problem statement that led to this decision. What situation are we facing? What constraints do we have?>

### Business Context
- <Business driver 1>
- <Business driver 2>

### Technical Context
- <Technical constraint 1>
- <Technical constraint 2>

### Stakeholders
- **Primary**: <Who is most affected by this decision?>
- **Secondary**: <Who else is impacted?>

---

## 🔍 Decision Drivers

<List the factors that influenced this decision>

- **Driver 1**: <Description and importance>
- **Driver 2**: <Description and importance>
- **Driver 3**: <Description and importance>

---

## 🎨 Considered Options

### Option 1: <Option Name>
**Description**: <Brief description of the option>

**Pros**:
- ✅ <Positive aspect 1>
- ✅ <Positive aspect 2>

**Cons**:
- ❌ <Negative aspect 1>
- ❌ <Negative aspect 2>

**Implementation Effort**: <High/Medium/Low>

### Option 2: <Option Name>
**Description**: <Brief description of the option>

**Pros**:
- ✅ <Positive aspect 1>
- ✅ <Positive aspect 2>

**Cons**:
- ❌ <Negative aspect 1>
- ❌ <Negative aspect 2>

**Implementation Effort**: <High/Medium/Low>

### Option 3: <Option Name>
**Description**: <Brief description of the option>

**Pros**:
- ✅ <Positive aspect 1>
- ✅ <Positive aspect 2>

**Cons**:
- ❌ <Negative aspect 1>
- ❌ <Negative aspect 2>

**Implementation Effort**: <High/Medium/Low>

---

## ✅ Decision Outcome

**Chosen Option**: <Selected option>

**Rationale**: <Explain why this option was chosen over the others>

### Implementation Plan
1. **Phase 1**: <Description and timeline>
2. **Phase 2**: <Description and timeline>
3. **Phase 3**: <Description and timeline>

### Success Criteria
- <Measurable criterion 1>
- <Measurable criterion 2>
- <Measurable criterion 3>

---

## 📊 Consequences

### Positive Consequences
- ✅ <Positive outcome 1>
- ✅ <Positive outcome 2>
- ✅ <Positive outcome 3>

### Negative Consequences
- ❌ <Negative outcome 1>
- ❌ <Negative outcome 2>
- ❌ <Negative outcome 3>

### Neutral Consequences
- ⚪ <Neutral outcome 1>
- ⚪ <Neutral outcome 2>

---

## 🔄 Follow-up Actions

### Immediate Actions
- [ ] <Action item 1> - <Owner> - <Due date>
- [ ] <Action item 2> - <Owner> - <Due date>

### Future Considerations
- <Future consideration 1>
- <Future consideration 2>

### Review Schedule
- **First Review**: <Date> - <Purpose>
- **Regular Reviews**: <Frequency> - <Criteria for review>

---

## 📚 References

### Related ADRs
- [ADR-XXX: Related Decision](./adr-xxx.mdx)

### External References
- [Reference 1](https://example.com)
- [Reference 2](https://example.com)

### Internal Documentation
- [Related Spec](../milestones/milestone-XX.mdx)
- [Domain Spec](../domains/domain-name.mdx)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 0.1.0 | <YYYY-MM-DD> | Initial draft | <Author> |

<Callout emoji="📝">
This ADR should be updated when the decision is implemented, reviewed, or superseded by a new decision.
</Callout>
