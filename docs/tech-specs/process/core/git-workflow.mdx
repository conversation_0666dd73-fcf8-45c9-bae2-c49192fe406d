---
title: "Git Workflow Process"
description: "Branching strategies, commit standards, and release processes"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["process", "git", "workflow", "branching"]
authors: ["nitishMehrotra"]
owner: ["development-team"]
---

# Git Workflow Process

> **🎯 Purpose:** This document defines the git workflow, branching strategies, commit standards, and release processes for the WorkflowMapperAgent project.

---

## 🚀 QUICK REFERENCE

### Branch Naming Conventions
- **Milestone**: `milestone/m{X}-{short-description}` (e.g., `milestone/m1-static-graph-builder`)
- **Task**: `m{X}/task-{##}-{short-description}` (e.g., `m1/task-01-parser-setup`)
- **Hotfix**: `hotfix/{issue-description}` (e.g., `hotfix/security-vulnerability`)

### Essential Git Commands
```bash
# Create milestone branch
git checkout main && git pull origin main
git checkout -b milestone/m1-static-graph-builder
git push -u origin milestone/m1-static-graph-builder

# Create task branch
git checkout milestone/m1-static-graph-builder
git checkout -b m1/task-01-parser-setup
git push -u origin m1/task-01-parser-setup

# Merge task to milestone (squash)
git checkout milestone/m1-static-graph-builder
git merge --squash m1/task-01-parser-setup
git commit -m "feat(parser): implement workflow file parser setup"
```

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```
**Types**: feat, fix, docs, style, refactor, test, chore
**Examples**: `feat(parser): add JSON-LD output support`, `fix(cli): resolve argument parsing issue`

### Merge Strategy
- **Task → Milestone**: Squash merge (clean history)
- **Milestone → Main**: Merge commit (preserve milestone context)
- **Hotfix → Main**: Direct merge (immediate deployment)

---

## 🌳 Branch Hierarchy & Workflow

### Branch Structure
```
main → milestone/m{X}-{description} → m{X}/task-{##}-{description}
```

### Branch Lifecycle
1. **Milestone**: Created from main, long-lived for milestone development
2. **Task**: Created from milestone, merged back with squash
3. **Support**: Hotfix/bugfix branches for immediate fixes

---

### Commit Types & Scopes
- **Types**: feat, fix, docs, style, refactor, test, chore
- **Scopes**: api, web, shared, docs, ci, deps
- **Example**: `feat(api): add JSON-LD output format support`

---

## 🔄 Core Procedures

### Milestone Workflow
1. **Create milestone branch** from main → **Update specification** → **Create work log**
2. **Create task branches** from milestone → **Implement** → **Create PR** → **Review** → **Merge**
3. **Complete milestone** → **Integration testing** → **Merge to main** → **Release**

### Code Review Requirements
- **Automated checks** pass (CI, tests, linting)
- **Peer review** for significant changes
- **Documentation review** for user-facing changes
- **Approval** from milestone owner

---

### Release Process
1. **Milestone completion** → **Integration testing** → **Documentation update** → **Acceptance tests**
2. **Version bump** → **Changelog update** → **Merge to main** → **Create tag** → **Deploy**
3. **Post-release**: Clean up branches, update docs, create work log

---

## 🛠️ Configuration & Quality

### Git Configuration Essentials
```bash
# Enable automatic rebase and helpful aliases
git config pull.rebase true
git config push.default current
git config alias.co checkout
git config alias.st status
```

### Quality Gates
- **Automated tests** passing + **Code coverage** thresholds met
- **Security scans** completed + **Documentation** updated
- **Acceptance criteria** validated

### Success Metrics
- **Clean merge rate**: >95% conflict-free merges
- **Review turnaround**: <24 hours for reviews
- **CI success rate**: >90% first-pass CI success

---

## 🔗 Related Resources

### Process Integration
- **Milestone Implementation**: [`milestone-implementation.mdx`](./milestone-implementation.mdx)
- **Quality Assurance**: [`quality-assurance.mdx`](./quality-assurance.mdx)
- **Documentation**: [`documentation.mdx`](./documentation.mdx)

---

**Review Frequency**: Quarterly or when workflow issues identified