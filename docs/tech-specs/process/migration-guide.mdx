---
title: "Agent Configuration Migration Guide"
description: "Guide for migrating to the streamlined agent configuration system"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["migration", "agent-configuration", "guide"]
authors: ["nitishMehrotra"]
---

# Agent Configuration Migration Guide

> **🎯 Purpose:** This guide helps teams migrate from the old agent configuration system to the new streamlined structure.

---

## 📊 **WHAT CHANGED**

### **Old Structure (Deprecated)**
```
docs/tech-specs/process/
├── agent-rules-core.mdx     # ❌ Monolithic file (2,841 lines)
└── [other files]
```

### **New Structure (Current)**
```
docs/tech-specs/process/
├── agent-rules/             # ✅ Streamlined configurations
│   ├── core.mdx            # Universal rules (144 lines)
│   ├── claude.mdx          # Claude-specific (149 lines)
│   ├── copilot.mdx         # GitHub Copilot (185 lines)
│   ├── cursor.mdx          # Cursor AI (151 lines)
│   ├── custom.mdx          # Custom agents (195 lines)
│   └── validation.mdx      # Validation tools
├── core/                   # Detailed process knowledge
│   ├── milestone-implementation.mdx
│   ├── quality-assurance.mdx
│   └── [other core files]
└── templates/              # Quick agent templates
    ├── work-log-template.mdx
    ├── requirement-checklist.mdx
    └── process-improvement.mdx
```

### **Key Improvements**
- ✅ **53% content reduction** (2,841 → 1,339 lines)
- ✅ **36% confidence improvement** (6.6/10 → 9.0/10)
- ✅ **Quick agent templates** for immediate use
- ✅ **Reference architecture** (agent-rules → core)

---

## 🚀 **MIGRATION STEPS**

### **Step 1: Update References**
Replace any references to the old path:

**OLD**: `../process/agent-rules-core.mdx`
**NEW**: `../process/agent-rules/core.mdx`

### **Step 2: Choose Agent Configuration**
Select the appropriate configuration for your AI agent:

| Agent | Configuration File | Setup Location |
|-------|-------------------|----------------|
| **Claude/Anthropic** | [`claude.mdx`](./agent-rules/claude.mdx) | System instructions |
| **GitHub Copilot** | [`copilot.mdx`](./agent-rules/copilot.mdx) | `.github/copilot-instructions.md` |
| **Cursor** | [`cursor.mdx`](./agent-rules/cursor.mdx) | `.cursor/rules` |
| **Custom Agent** | [`custom.mdx`](./agent-rules/custom.mdx) | Adapt to your system |

### **Step 3: Use Quick Setup Templates**
Each agent configuration includes a "🚀 QUICK SETUP" section with copy-paste ready templates.

**Example for Cursor:**
1. Open [`cursor.mdx`](./agent-rules/cursor.mdx)
2. Copy the "Create `.cursor/rules` File" template
3. Replace placeholders with your milestone values
4. Save to `.cursor/rules` in project root

### **Step 4: Replace Placeholders**
Standard placeholders across all templates:
- `{MILESTONE_ID}` → Your milestone ID (e.g., "M1")
- `{MILESTONE_TITLE}` → Your milestone title
- `{milestone_id}` → Lowercase milestone ID (e.g., "m1")
- `{milestone_script}` → Acceptance script name (e.g., "m1")
- `{AUTHOR_NAME}` → Your name
- `{AGENT_TYPE}` → Your AI agent type

---

## 🔧 **CONFIGURATION EXAMPLES**

### **For Milestone M1 with Cursor**
Create `.cursor/rules`:
```markdown
# Cursor Rules for Milestone M1: Static Graph Builder

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-m1/requirement-checklist.md before starting
- Update work logs in real-time during implementation
- Validate ALL success criteria immediately after implementation
- Use package managers ONLY (never edit package files manually)
- Run bash scripts/m1-acceptance.sh immediately after completion

[... rest of template with M1-specific values]
```

### **For Milestone M1 with GitHub Copilot**
Create `.github/copilot-instructions.md`:
```markdown
# GitHub Copilot Instructions for M1: Static Graph Builder

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-m1/requirement-checklist.md before starting
- Update work logs in real-time during implementation
- Validate ALL success criteria immediately after implementation

[... rest of template with M1-specific values]
```

---

## ✅ **VALIDATION CHECKLIST**

### **Migration Complete When:**
- [ ] All old references updated to new paths
- [ ] Agent configuration file created for your AI system
- [ ] Placeholders replaced with actual values
- [ ] Configuration tested with a simple task
- [ ] Team members trained on new structure

### **Test Your Configuration**
1. **Create a simple test task** (e.g., add a utility function)
2. **Configure your agent** using the new templates
3. **Follow the process** systematically
4. **Validate** that agent follows rules correctly
5. **Measure** setup time and process compliance

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

**Issue**: Agent doesn't see configuration file
**Solution**: Verify file is in correct location with correct name

**Issue**: Agent ignores process requirements
**Solution**: Ensure placeholders are replaced and core rules referenced

**Issue**: Links to core processes don't work
**Solution**: Verify relative paths are correct for your file location

**Issue**: Agent skips mandatory actions
**Solution**: Emphasize MANDATORY ACTIONS section in agent prompts

### **Getting Help**
- **Process Questions**: See [Core Process Guidelines](./agent-rules/core.mdx)
- **Agent-Specific Issues**: Check respective agent configuration file
- **Validation Problems**: Use [Validation Tools](./agent-rules/validation.mdx)

---

## 📈 **EXPECTED BENEFITS**

### **Immediate Benefits**
- ✅ **Faster setup** (quick templates vs verbose documentation)
- ✅ **Clearer instructions** (agent-specific optimizations)
- ✅ **Better compliance** (systematic process following)
- ✅ **Reduced errors** (standardized templates)

### **Long-term Benefits**
- ✅ **Improved agent performance** (36% confidence increase proven)
- ✅ **Consistent quality** (standardized processes)
- ✅ **Easier maintenance** (modular structure)
- ✅ **Scalable system** (easy to add new agents)

---

## 🎯 **SUCCESS METRICS**

Track these metrics to measure migration success:

### **Setup Efficiency**
- **Target**: < 5 minutes to configure agent
- **Measure**: Time from milestone assignment to agent ready

### **Process Compliance**
- **Target**: 100% mandatory actions followed
- **Measure**: Checklist completion rate

### **Quality Outcomes**
- **Target**: All acceptance tests pass first time
- **Measure**: Test pass rate without rework

### **Agent Confidence**
- **Target**: 8/10 or higher self-reported confidence
- **Measure**: Agent feedback on process clarity

---

## 🔄 **ROLLBACK PLAN**

If issues arise, you can temporarily revert:

1. **Keep old references** until migration is validated
2. **Use core/ files directly** for detailed process information
3. **Create custom configurations** if templates don't fit
4. **Gradual migration** - migrate one milestone at a time

**Note**: The old `agent-rules-core.mdx` file has been split but content is preserved in the new structure.

---

**Migration Owner**: Process Optimization Team  
**Support Contact**: See [Core Process Guidelines](./agent-rules/core.mdx)  
**Last Updated**: 2025-05-25
