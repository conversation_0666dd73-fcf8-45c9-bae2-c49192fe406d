---
title: ADR-008 — Git Diff Integration for Incremental Updates
description: Strategy for using git diff to detect changed files and enable incremental knowledge graph updates with optimal performance.
created: 2025-06-01
updated: 2025-06-01
version: 1.0.0
status: Accepted
tags: [adr, architecture, git-diff, incremental-updates, simple-git]
authors: [nitishMehrotra]
---



<Callout emoji="🏗️">
<strong>Architectural Decision Record.</strong> This document captures an important architectural decision made for the WorkflowMapperAgent project.
</Callout>

> **📋 Process Guidelines:** For complete ADR creation, review, and management processes, see [Core Process Guidelines](../process/agent-rules/core.mdx#🏗️-architectural-decision-process).

---

## 📋 Decision Summary

**ID**: ADR-008
**Date**: 2025-06-01
**Status**: Accepted
**Deciders**: WorkflowMapper Team
**Technical Story**: Milestone M1.2 - Git Diff Integration for Incremental Knowledge Graph Updates

---

## 🎯 Context and Problem Statement

As part of the bidirectional sync system (ADR-006), we need an efficient mechanism to detect which files have changed since the last knowledge graph update. This enables incremental processing instead of full repository scans, dramatically improving performance for large codebases and CI/CD workflows.

### Business Context
- **Build Performance**: Full repository scans become prohibitively slow as codebases grow (>10k files)
- **CI/CD Efficiency**: Pull request validation must complete quickly to maintain developer velocity
- **Resource Optimization**: Minimize computational resources and costs in cloud CI environments
- **Developer Experience**: Fast feedback loops are essential for productive development workflows

### Technical Context
- **Git-based Workflow**: All development uses git with branch-based feature development
- **Monorepo Structure**: Large TypeScript/Node.js monorepo with multiple packages and domains
- **CI/CD Integration**: GitHub Actions workflows must complete within reasonable time limits
- **Existing Infrastructure**: simple-git library already specified in M1.2 toolchain
- **Performance Requirements**: Target 90%+ performance improvement over full scans

### Stakeholders
- **Primary**: Backend developers, DevOps engineers, CI/CD pipeline maintainers
- **Secondary**: Frontend developers, technical writers, external contributors

---

## 🔍 Decision Drivers

- **Performance Optimization**: Achieve 90%+ performance improvement over full repository scans
- **CI/CD Integration**: Seamless integration with GitHub Actions and other CI systems
- **Reliability**: Accurate change detection without missing modified files
- **Flexibility**: Support various git diff scenarios (branch comparisons, commit ranges, working directory)
- **Error Handling**: Robust handling of git edge cases (merge conflicts, binary files, large diffs)
- **Maintainability**: Use established, well-maintained libraries rather than custom git integration
- **Cross-platform Compatibility**: Work consistently across Linux, macOS, and Windows environments

---

## 🎨 Considered Options

### Option 1: Shell Command Git Integration
**Description**: Execute git commands directly via child_process.exec() or similar shell execution

**Pros**:
- ✅ Direct access to all git functionality
- ✅ No external dependencies
- ✅ Maximum flexibility for complex git operations

**Cons**:
- ❌ Platform-specific command variations (Windows vs Unix)
- ❌ Complex error handling and output parsing
- ❌ Security risks with shell injection
- ❌ Difficult to test and mock
- ❌ Poor error messages and debugging

**Implementation Effort**: Medium

### Option 2: Native Git Bindings (NodeGit)
**Description**: Use native Git bindings like NodeGit for direct libgit2 integration

**Pros**:
- ✅ High performance native implementation
- ✅ Complete git functionality access
- ✅ No shell command dependencies

**Cons**:
- ❌ Complex native compilation requirements
- ❌ Platform-specific build issues
- ❌ Large dependency size and complexity
- ❌ Difficult CI/CD integration
- ❌ Higher maintenance overhead

**Implementation Effort**: High

### Option 3: simple-git Library (CHOSEN)
**Description**: Use simple-git library for TypeScript-friendly git operations with promise-based API

**Pros**:
- ✅ Mature, well-maintained library (3.22.0 stable)
- ✅ TypeScript-first design with excellent type safety
- ✅ Promise-based API integrates well with async/await
- ✅ Comprehensive git diff functionality
- ✅ Excellent error handling and debugging
- ✅ Cross-platform compatibility
- ✅ Easy to test and mock
- ✅ Lightweight (no native dependencies)

**Cons**:
- ❌ External dependency (minimal risk - 2M+ weekly downloads)
- ❌ Requires git binary on system (acceptable for development environments)

**Implementation Effort**: Low

---

## ✅ Decision Outcome

**Chosen Option**: simple-git Library Integration

**Rationale**: simple-git provides the optimal balance of functionality, reliability, and maintainability. Its TypeScript-first design aligns perfectly with our codebase, while its mature ecosystem and extensive usage (2M+ weekly downloads) provides confidence in long-term stability. The promise-based API integrates seamlessly with our existing async patterns.

### Implementation Plan
1. **Phase 1**: Install simple-git and implement basic diff detection for file changes
2. **Phase 2**: Add support for various diff scenarios (branch comparisons, commit ranges)
3. **Phase 3**: Implement error handling for edge cases (merge conflicts, binary files)
4. **Phase 4**: Add performance optimizations and caching strategies

### Success Criteria
- Detect file changes with 100% accuracy across all supported git scenarios
- Achieve 90%+ performance improvement over full repository scans
- Handle edge cases gracefully with clear error messages
- Support all required diff scenarios: --since HEAD~1, --since origin/main, working directory changes
- Comprehensive test coverage (≥95%) for all git integration scenarios

---

## 📊 Consequences

### Positive Consequences
- ✅ **Dramatic Performance Improvement**: 90%+ reduction in processing time for large repositories
- ✅ **CI/CD Optimization**: Faster pull request validation and reduced cloud compute costs
- ✅ **Developer Productivity**: Near-instant feedback for incremental changes
- ✅ **Scalability**: Architecture supports repositories of any size without performance degradation
- ✅ **Reliability**: Mature simple-git library with extensive real-world usage and testing
- ✅ **Maintainability**: TypeScript-first design with excellent debugging and error handling

### Negative Consequences
- ❌ **Git Dependency**: Requires git binary to be available in all environments (acceptable constraint)
- ❌ **Complexity**: More complex than full-scan approach, requiring careful state management
- ❌ **Edge Case Handling**: Need to handle git edge cases like merge conflicts and binary files

### Neutral Consequences
- ⚪ **External Dependency**: simple-git is well-maintained with 2M+ weekly downloads (low risk)
- ⚪ **Learning Curve**: Team needs to understand git diff scenarios and edge cases

---

## 🚀 Practical Usage

### Core Git Diff Scenarios

```typescript
import simpleGit from 'simple-git';

const git = simpleGit();

// Scenario 1: Changes since specific commit
const changedFiles = await git.diff(['--name-only', 'HEAD~1']);

// Scenario 2: Changes since main branch
const branchChanges = await git.diff(['--name-only', 'origin/main']);

// Scenario 3: Working directory changes
const workingChanges = await git.diff(['--name-only']);

// Scenario 4: Staged changes
const stagedChanges = await git.diff(['--name-only', '--cached']);
```

### Error Handling Strategy

```typescript
interface DiffResult {
  changedFiles: string[];
  errors: GitError[];
  warnings: GitWarning[];
  performance: {
    totalFiles: number;
    changedFiles: number;
    processingTime: number;
    performanceGain: number;
  };
}

async function getChangedFiles(since: string): Promise<DiffResult> {
  try {
    const git = simpleGit();
    const changedFiles = await git.diff(['--name-only', since]);
    return {
      changedFiles: changedFiles.split('\n').filter(Boolean),
      errors: [],
      warnings: [],
      performance: calculatePerformanceMetrics(changedFiles)
    };
  } catch (error) {
    return handleGitError(error);
  }
}
```

### CI/CD Integration Pattern

```yaml
# GitHub Actions workflow
- name: Sync Knowledge Graph (Incremental)
  run: |
    # Get changed files since main branch
    pnpm run sync-kg -- --since origin/main --dry-run

    # Only proceed if changes detected
    if [ $? -eq 0 ]; then
      pnpm run sync-kg -- --since origin/main
    fi
```

---

## 🔄 Follow-up Actions

### Immediate Actions
- [ ] Install simple-git dependency (v3.22.0) - BE Team - Week 1
- [ ] Implement diffGit.ts with basic change detection - BE Team - Week 1
- [ ] Add support for --since parameter variations - BE Team - Week 1
- [ ] Create comprehensive test suite for git scenarios - QA Team - Week 2

### Future Considerations
- **Performance Monitoring**: Add metrics collection for diff operation performance
- **Caching Strategy**: Implement intelligent caching for repeated diff operations
- **Advanced Git Features**: Support for submodules, sparse checkouts, and LFS files
- **Conflict Resolution**: Automated handling of merge conflicts in diff detection

### Review Schedule
- **First Review**: Q2 2025 - Evaluate performance improvements and edge case handling
- **Regular Reviews**: Quarterly - Assess new git scenarios and optimization opportunities

---

## 📚 References

### Related ADRs
- [ADR-006: Bidirectional Sync Architecture for Knowledge Graph](./adr-006-bidirectional-sync-architectur.mdx)
- [ADR-007: JSDoc Annotation Parsing Strategy](./adr-007-jsdoc-annotation-parsing-strat.mdx)

### External References
- [simple-git Documentation](https://github.com/steveukx/git-js)
- [Git Diff Documentation](https://git-scm.com/docs/git-diff)
- [GitHub Actions Git Context](https://docs.github.com/en/actions/learn-github-actions/contexts#github-context)

### Internal Documentation
- [Milestone M1.2: Bidirectional Sync](../milestones/milestone-M1.2.mdx)
- [CI/CD Workflows Documentation](../domains/ci-cd.mdx)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-06-01 | Complete ADR for git diff integration strategy | nitishMehrotra |

<Callout emoji="✅">
This ADR documents the accepted strategy for git diff integration using simple-git library for incremental updates.
</Callout>
