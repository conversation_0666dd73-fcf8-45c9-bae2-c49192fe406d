---
title: ADR-005 — Knowledge Graph System Architecture
description: Unified architecture combining specification parsing (M0.1) and static code analysis (M1.1) for comprehensive knowledge graph generation.
created: 2025-01-27
updated: 2025-01-27
version: 1.0.0
status: Accepted
tags: [adr, architecture, knowledge-graph, static-analysis, specifications]
authors: [WorkflowMapper Team]
---



<Callout emoji="🏗️">
<strong>Architectural Decision Record.</strong> This document captures an important architectural decision made for the WorkflowMapperAgent project.
</Callout>

> **📋 Process Guidelines:** For complete ADR creation, review, and management processes, see [Core Process Guidelines](../agent-rules/core.mdx#🏗️-architectural-decision-process).

---

## 📋 Decision Summary

**ID**: ADR-005
**Date**: 2025-01-27
**Status**: Accepted
**Deciders**: WorkflowMapper Team
**Technical Story**: Milestone M0.1 + M1.1 Integration
**Implementation**: Complete - Production Ready

---

## 🎯 Context and Problem Statement

Following the successful implementation of Milestone M0.1 (Specification Parser) and Milestone M1.1 (Static Code Parser), we need to establish a unified architecture for comprehensive knowledge graph generation. The system must bridge the gap between documentation and implementation, enabling automated discovery of relationships between specifications and actual code.

### Business Context
- **Documentation-Code Drift**: Manual maintenance of relationships between specs and code is error-prone and time-consuming
- **Knowledge Discovery**: Need automated way to understand system architecture from both documentation and implementation
- **CI/CD Integration**: Require automated validation that code matches specifications
- **Developer Productivity**: Enable developers to quickly understand system relationships and dependencies

### Technical Context
- **Existing Monorepo**: Must work with current TypeScript/Node.js monorepo structure
- **Multiple Data Sources**: MDX specification files + source code in Python/JavaScript
- **Output Requirements**: JSON-LD for machine processing, YAML for human review
- **Performance**: Must handle large codebases efficiently
- **Extensibility**: Architecture must support additional languages and analysis types

### Stakeholders
- **Primary**: Development team, technical writers, system architects
- **Secondary**: DevOps engineers, product managers, external integrators

---

## 🔍 Decision Drivers

- **Unified Knowledge Representation**: Need single source of truth combining documentation and code analysis
- **Automated Relationship Discovery**: Reduce manual effort in maintaining spec-to-code mappings
- **CI/CD Integration**: Enable automated validation that implementations match specifications
- **Multiple Output Formats**: Support both machine-readable (JSON-LD) and human-readable (YAML) formats
- **Extensibility**: Architecture must support future languages and analysis types
- **Performance**: Handle large codebases efficiently without blocking development workflows
- **Developer Experience**: Provide clear CLI interface and comprehensive error reporting

---

## 🎨 Considered Options

### Option 1: Separate Systems
**Description**: Maintain separate tools for specification parsing and code analysis with manual integration

**Pros**:
- ✅ Simple to implement initially
- ✅ Clear separation of concerns
- ✅ Independent development cycles

**Cons**:
- ❌ Manual effort to correlate data
- ❌ Inconsistent output formats
- ❌ No unified relationship discovery
- ❌ Duplicate infrastructure

**Implementation Effort**: Low

### Option 2: External Graph Database
**Description**: Use Neo4j or similar graph database to store and query relationships

**Pros**:
- ✅ Powerful query capabilities
- ✅ Built-in graph algorithms
- ✅ Scalable for large datasets

**Cons**:
- ❌ Additional infrastructure complexity
- ❌ Learning curve for graph query languages
- ❌ Deployment and maintenance overhead
- ❌ Not suitable for CI/CD pipelines

**Implementation Effort**: High

### Option 3: Unified CLI with Integrated Architecture (CHOSEN)
**Description**: Single CLI tool that combines specification parsing and code analysis with unified knowledge graph output

**Pros**:
- ✅ Single command for complete analysis
- ✅ Consistent JSON-LD + YAML output
- ✅ Automated relationship discovery
- ✅ CI/CD friendly (no external dependencies)
- ✅ Extensible architecture for new languages
- ✅ Tree-sitter for reliable parsing

**Cons**:
- ❌ More complex initial implementation
- ❌ Tighter coupling between components

**Implementation Effort**: Medium

---

## ✅ Decision Outcome

**Chosen Option**: Unified CLI with Integrated Architecture

**Rationale**: This option provides the best balance of functionality, maintainability, and CI/CD integration. The unified approach eliminates manual correlation work while maintaining simplicity for deployment and usage. Tree-sitter provides reliable parsing, and the modular architecture supports future extensions.

### Implementation Plan
1. **Phase 1 (M0.1)**: Specification Parser - MDX parsing with JSON-LD/YAML output ✅ **COMPLETE**
2. **Phase 2 (M1.1)**: Static Code Parser - Tree-sitter integration for Python/JavaScript ✅ **COMPLETE**
3. **Phase 3 (M1.1)**: Knowledge Graph Integration - Unified output combining specs and code ✅ **COMPLETE**

### Success Criteria
- ✅ Single CLI command generates complete knowledge graph
- ✅ Support for MDX specifications and Python/JavaScript source code
- ✅ JSON-LD output with proper semantic structure
- ✅ YAML output for human readability
- ✅ CI/CD integration with GitHub Actions
- ✅ Test coverage ≥ 80% (achieved 91.84%)
- ✅ Comprehensive error handling and reporting

---

## 📊 Consequences

### Positive Consequences
- ✅ **Automated Knowledge Discovery**: Developers can now automatically discover relationships between specifications and code
- ✅ **Reduced Documentation Drift**: Automated validation ensures specs and code stay synchronized
- ✅ **Improved Developer Productivity**: Single command provides comprehensive system understanding
- ✅ **CI/CD Integration**: Automated validation prevents specification-code mismatches in production
- ✅ **Extensible Architecture**: Easy to add support for new languages and analysis types
- ✅ **High Test Coverage**: 91.84% coverage ensures reliability and maintainability

### Negative Consequences
- ❌ **Increased Complexity**: More complex than separate tools, requiring understanding of both domains
- ❌ **Build Dependencies**: Requires Tree-sitter compilation for language grammars
- ❌ **Learning Curve**: Developers need to understand JSON-LD structure for advanced usage

### Neutral Consequences
- ⚪ **Node.js Dependency**: Requires Node.js runtime, but already part of existing toolchain
- ⚪ **File Size**: Generated knowledge graphs can be large for big codebases, but manageable with proper tooling

---

## 🚀 Practical Usage

### Architecture Overview

The system consists of three main components:

1. **@workflow-mapper/spec-parser-lib** (M0.1): Parses MDX specification files
2. **@workflow-mapper/code-parser-lib** (M1.1): Analyzes source code using Tree-sitter
3. **@workflow-mapper/kg-cli** (Enhanced): Unified CLI that combines both parsers

### Basic Usage

```bash
# Parse specifications only
pnpm run build-kg docs/tech-specs

# Parse code only
pnpm run build-kg --code src/ .

# Combined analysis (recommended)
pnpm run build-kg --code src/ docs/tech-specs

# Dry run for validation
pnpm run build-kg --code src/ --dry-run docs/tech-specs

# Language-specific parsing
pnpm run build-kg --code src/ --languages py docs/tech-specs
```

### Real Example: Current Repository

Running on our own repository demonstrates the system's capabilities:

```bash
cd code
pnpm run build-kg --code packages/code-parser-lib/tests/fixtures --dry-run ../docs/tech-specs
```

**Output:**
- **Specs found**: 43 (milestones, ADRs, domains, etc.)
- **Functions found**: 15 (from test fixtures)
- **Call graph edges**: 13 (function call relationships)
- **Milestones**: 12 (including M0.1, M1.1, etc.)

### Output Formats

**JSON-LD Structure:**
```json
{
  "@context": "https://schema.org/",
  "@graph": [
    {
      "@id": "milestone:m0-1",
      "@type": "Milestone",
      "title": "Specification Parser",
      "status": "Complete"
    },
    {
      "@id": "function:abc12345",
      "@type": "function",
      "name": "parseFile",
      "signature": "parseFile(filePath)",
      "lang": "python"
    },
    {
      "@id": "call:def67890",
      "@type": "workflow_calls",
      "source": "function:abc12345",
      "target": "function:xyz98765",
      "confidence": 1.0
    }
  ]
}
```

### CI/CD Integration

**GitHub Actions Workflow:**
```yaml
name: Knowledge Graph Validation
on: [push, pull_request]
jobs:
  validate-kg:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: cd code && pnpm install
      - run: cd code && pnpm run build-kg --code src/ --dry-run ../docs/tech-specs
```

---

## 🔄 Follow-up Actions

### Immediate Actions
- [x] **Update ADR Log** - WorkflowMapper Team - 2025-01-27
- [x] **Document Usage Examples** - WorkflowMapper Team - 2025-01-27
- [ ] **Create Developer Guide** - WorkflowMapper Team - Q1 2025
- [ ] **Performance Benchmarking** - WorkflowMapper Team - Q1 2025

### Future Considerations
- **Additional Languages**: TypeScript, Go, Rust support (M1.2+)
- **Cross-file Analysis**: Import/export relationship mapping
- **Performance Optimization**: Incremental parsing and caching
- **Advanced Relationships**: Semantic analysis for spec-to-code mapping
- **Visualization Tools**: Interactive graph exploration interface

### Review Schedule
- **First Review**: Q2 2025 - Evaluate performance and usage patterns
- **Regular Reviews**: Quarterly - Assess new language support and optimization needs

---

## 📚 References

### Related ADRs
- [ADR-001: Monorepo Structure](./adr-001-monorepo.mdx)
- [ADR-002: TypeScript Adoption](./adr-002-typescript.mdx)
- [ADR-003: JSON-LD for Knowledge Graphs](./adr-003-jsonld.mdx)

### External References
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/)
- [JSON-LD Specification](https://json-ld.org/)
- [Schema.org Vocabulary](https://schema.org/)

### Internal Documentation
- [Milestone M0.1: Specification Parser](../milestones/milestone-M0.1.mdx)
- [Milestone M1.1: Static Code Parser](../milestones/milestone-M1.1.mdx)
- [Code Parser Domain](../domains/code-parser.mdx)
- [Acceptance Tests](../../scripts/acceptance/milestone-M1.1.sh)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-01-27 | Complete ADR documenting unified knowledge graph architecture | WorkflowMapper Team |

<Callout emoji="✅">
This ADR documents the completed implementation of the unified knowledge graph system combining Milestone M0.1 and M1.1 deliverables.
</Callout>
