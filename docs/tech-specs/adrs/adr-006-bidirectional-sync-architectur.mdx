---
title: ADR-006 — Bidirectional Sync Architecture for Knowledge Graph
description: Architecture for bidirectional synchronization between specifications and code using incremental git diff and annotation parsing.
created: 2025-06-01
updated: 2025-06-01
version: 1.0.0
status: Accepted
tags: [adr, architecture, bidirectional-sync, knowledge-graph, git-diff]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🏗️">
<strong>Architectural Decision Record.</strong> This document captures an important architectural decision made for the WorkflowMapperAgent project.
</Callout>

> **📋 Process Guidelines:** For complete ADR creation, review, and management processes, see [Core Process Guidelines](../process/agent-rules/core.mdx#🏗️-architectural-decision-process).

---

## 📋 Decision Summary

**ID**: ADR-006
**Date**: 2025-06-01
**Status**: Accepted
**Deciders**: WorkflowMapper Team
**Technical Story**: Milestone M1.2 - Bidirectional Sync & Incremental Diff

---

## 🎯 Context and Problem Statement

Following the successful implementation of M0.1 (Specification Parser) and M1.1 (Static Code Parser), we need to establish bidirectional synchronization between specifications and code. The current system can parse both specs and code independently, but lacks the ability to track relationships, detect changes incrementally, and maintain consistency between documentation and implementation.

### Business Context
- **Documentation-Code Drift**: Manual maintenance of spec-to-code relationships is error-prone and time-consuming
- **Change Detection**: Need automated way to detect when specs or code changes affect the knowledge graph
- **Coverage Tracking**: Require visibility into implementation coverage of specifications
- **CI/CD Integration**: Enable automated validation that prevents specification-code mismatches

### Technical Context
- **Existing Foundation**: M1.1 provides robust code parsing with Tree-sitter and knowledge graph generation
- **Git-based Workflow**: Development team uses git for version control with PR-based workflows
- **Monorepo Structure**: TypeScript/Node.js monorepo with pnpm workspaces
- **Performance Requirements**: Must handle large codebases efficiently without blocking development workflows
- **CI/CD Constraints**: Must work in GitHub Actions without external dependencies

### Stakeholders
- **Primary**: Development team, technical writers, system architects
- **Secondary**: DevOps engineers, product managers, external integrators

---

## 🔍 Decision Drivers

- **Incremental Processing**: Only process changed files to maintain performance at scale
- **Automated Relationship Discovery**: Reduce manual effort in maintaining spec-to-code mappings
- **Coverage Metrics**: Provide quantitative measures of implementation completeness
- **CI/CD Integration**: Enable automated validation in pull request workflows
- **Confidence Tracking**: Track reliability of relationships over time
- **Stale Detection**: Identify when code changes make specifications outdated
- **Developer Experience**: Provide clear feedback and actionable insights

---

## 🎨 Considered Options

### Option 1: Full Repository Scan on Every Change
**Description**: Re-parse entire repository on every change, similar to current M1.1 approach

**Pros**:
- ✅ Simple implementation
- ✅ Always up-to-date
- ✅ No incremental complexity

**Cons**:
- ❌ Poor performance on large repositories
- ❌ Blocks CI/CD pipelines
- ❌ Wasteful resource usage
- ❌ Not scalable

**Implementation Effort**: Low

### Option 2: Database-driven Change Tracking
**Description**: Use external database to track changes and relationships

**Pros**:
- ✅ Sophisticated query capabilities
- ✅ Persistent state management
- ✅ Advanced relationship tracking

**Cons**:
- ❌ Additional infrastructure complexity
- ❌ Not suitable for CI/CD environments
- ❌ Deployment and maintenance overhead
- ❌ External dependency

**Implementation Effort**: High

### Option 3: Git Diff-based Incremental Sync (CHOSEN)
**Description**: Use git diff to identify changed files and incrementally update knowledge graph

**Pros**:
- ✅ Leverages existing git infrastructure
- ✅ Excellent performance characteristics
- ✅ CI/CD friendly (no external dependencies)
- ✅ Natural integration with development workflow
- ✅ Supports both file and annotation-level changes
- ✅ Enables confidence and coverage tracking

**Cons**:
- ❌ More complex implementation than full scan
- ❌ Requires careful state management

**Implementation Effort**: Medium

---

## ✅ Decision Outcome

**Chosen Option**: Git Diff-based Incremental Sync

**Rationale**: This option provides the best balance of performance, maintainability, and CI/CD integration. Git diff is a proven, reliable mechanism for change detection that aligns perfectly with developer workflows. The incremental approach ensures scalability while the annotation-based relationship tracking provides the bidirectional sync capabilities we need.

### Implementation Plan
1. **Phase 1**: Implement git diff detection using simple-git library
2. **Phase 2**: Add annotation parsing with comment-parser for JSDoc/TSDoc
3. **Phase 3**: Implement incremental graph updates with confidence scoring
4. **Phase 4**: Add coverage metrics and CI/CD integration

### Success Criteria
- Process only changed files (performance improvement >90% on large repos)
- Parse @implements annotations with 99%+ accuracy
- Generate confidence and coverage metrics for all milestones
- CI/CD integration with coverage threshold enforcement
- Comprehensive error handling and reporting

---

## 📊 Consequences

### Positive Consequences
- ✅ **Improved Performance**: Incremental processing reduces build times by 90%+ on large repositories
- ✅ **Automated Relationship Discovery**: Developers can automatically discover spec-to-code relationships via annotations
- ✅ **Coverage Visibility**: Quantitative metrics show implementation completeness for each milestone
- ✅ **CI/CD Integration**: Automated validation prevents specification-code drift in production
- ✅ **Developer Productivity**: Clear feedback on implementation status and stale specifications
- ✅ **Scalability**: Architecture supports repositories of any size without performance degradation

### Negative Consequences
- ❌ **Implementation Complexity**: More complex than full-scan approach, requiring careful state management
- ❌ **Learning Curve**: Developers need to understand annotation syntax and confidence scoring
- ❌ **Dependency on Git**: Relies heavily on git diff accuracy and availability

### Neutral Consequences
- ⚪ **Additional Dependencies**: Requires simple-git and comment-parser libraries (both well-maintained)
- ⚪ **Annotation Overhead**: Developers need to add @implements annotations to functions (minimal effort)

---

## 🚀 Practical Usage

### Core Architecture Components

1. **kg-sync-lib Package**: New package containing git diff and annotation parsing logic
2. **Enhanced kg-cli**: Extended with sync-kg command for incremental updates
3. **CI/CD Integration**: GitHub Actions workflow for automated validation

### Example Usage

```bash
# Incremental sync since last commit
pnpm run sync-kg -- --since HEAD~1

# Sync changes since main branch
pnpm run sync-kg -- --since origin/main

# Dry run for validation
pnpm run sync-kg -- --since HEAD~1 --dry-run
```

### Annotation Format

```typescript
/**
 * @implements milestone-M1.2#AnnotationParser
 * Parses JSDoc comments to extract implementation annotations
 */
function parseAnnotations(content: string): Annotation[] {
  // Implementation
}
```

---

## 🔄 Follow-up Actions

### Immediate Actions
- [ ] Implement kg-sync-lib package scaffolding - BE Team - Week 1
- [ ] Add simple-git integration for diff detection - BE Team - Week 1
- [ ] Implement comment-parser for annotation extraction - BE Team - Week 2
- [ ] Create CI/CD workflow for coverage validation - DevOps Team - Week 2

### Future Considerations
- **Cross-file Relationship Tracking**: Extend to track imports/exports between files
- **Advanced Confidence Scoring**: Machine learning-based confidence assessment
- **Real-time Sync**: WebSocket-based real-time updates for development environments
- **Multi-language Support**: Extend annotation parsing to Python, Go, Rust

### Review Schedule
- **First Review**: Q2 2025 - Evaluate performance and accuracy metrics
- **Regular Reviews**: Quarterly - Assess new language support and optimization needs

---

## 📚 References

### Related ADRs
- [ADR-005: Knowledge Graph System Architecture](./adr-005-knowledge-graph-system-archite.mdx)
- [ADR-007: JSDoc Annotation Parsing Strategy](./adr-007-jsdoc-annotation-parsing-strat.mdx)
- [ADR-008: Git Diff Integration for Incremental Updates](./adr-008-git-diff-integration-for-incre.mdx)

### External References
- [simple-git Documentation](https://github.com/steveukx/git-js)
- [comment-parser Documentation](https://github.com/syavorsky/comment-parser)
- [JSDoc Specification](https://jsdoc.app/)

### Internal Documentation
- [Milestone M1.2: Bidirectional Sync](../milestones/milestone-M1.2.mdx)
- [Knowledge Graph Domain](../domains/knowledge-graph.mdx)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-06-01 | Complete ADR for bidirectional sync architecture | nitishMehrotra |

<Callout emoji="✅">
This ADR documents the accepted architecture for M1.2 bidirectional sync implementation.
</Callout>
