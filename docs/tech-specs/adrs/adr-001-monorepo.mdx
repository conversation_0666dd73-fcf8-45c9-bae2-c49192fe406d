---
title: ADR-001 — Monorepo Structure with pnpm Workspaces
description: Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.
created: 2025-05-25
updated: 2025-05-25
version: 1.0.0
status: Accepted
tags: [adr, architecture, monorepo]
authors: [nitishMehrotra]
---



<Callout emoji="🏗️">
<strong>Architectural Decision Record.</strong> This document captures the decision to use a monorepo structure for the WorkflowMapperAgent project.
</Callout>

---

## 📋 Decision Summary

**ID**: ADR-001
**Date**: 2025-05-25
**Status**: Accepted
**Deciders**: Engineering Team
**Technical Story**: Initial project setup and architecture decisions

---

## 🎯 Context and Problem Statement

We need to organize multiple applications (API, Web frontend) and shared packages efficiently for the WorkflowMapperAgent project. The question is whether to use separate repositories for each component or a unified monorepo structure.

### Business Context
- Need to maintain consistency across frontend and backend
- Want to enable rapid development and iteration
- Require shared types and utilities between applications

### Technical Context
- TypeScript codebase with shared types
- Multiple deployable applications (API server, web frontend)
- Shared packages for common functionality
- Need for coordinated releases and versioning

### Stakeholders
- **Primary**: Development team, DevOps engineers
- **Secondary**: Product team, future contributors

---

## 🔍 Decision Drivers

- **Development velocity**: Faster iteration and cross-package changes
- **Type safety**: Shared TypeScript types between frontend and backend
- **Dependency management**: Unified dependency versions and security updates
- **Build coordination**: Coordinated builds and testing across packages
- **Code reuse**: Shared utilities and components

---

## 🎨 Considered Options

### Option 1: Separate Repositories
**Description**: Individual repositories for API, web frontend, and shared packages

**Pros**:
- ✅ Clear separation of concerns
- ✅ Independent deployment cycles
- ✅ Smaller repository sizes

**Cons**:
- ❌ Complex dependency management across repos
- ❌ Difficult to coordinate breaking changes
- ❌ Duplicated tooling and CI configuration
- ❌ Version synchronization challenges

**Implementation Effort**: High (multiple CI setups, complex release coordination)

### Option 2: Monorepo with Lerna
**Description**: Single repository with Lerna for package management

**Pros**:
- ✅ Unified dependency management
- ✅ Coordinated releases
- ✅ Shared tooling configuration

**Cons**:
- ❌ Lerna has maintenance concerns
- ❌ More complex than needed for our use case
- ❌ Additional learning curve

**Implementation Effort**: Medium

### Option 3: Monorepo with pnpm Workspaces
**Description**: Single repository using pnpm workspaces with apps/ and packages/ structure

**Pros**:
- ✅ Native workspace support in pnpm
- ✅ Efficient disk usage with hard links
- ✅ Fast installs and builds
- ✅ Simple configuration
- ✅ Excellent TypeScript integration

**Cons**:
- ❌ Larger repository size
- ❌ Slightly more complex CI setup initially

**Implementation Effort**: Low (built into pnpm)

---

## ✅ Decision Outcome

**Chosen Option**: Monorepo with pnpm Workspaces

**Rationale**: pnpm workspaces provide the best balance of simplicity, performance, and functionality for our needs. The native workspace support eliminates the need for additional tooling while providing excellent dependency management and build coordination.

### Implementation Plan
1. **Phase 1**: Set up pnpm-workspace.yaml and basic structure
2. **Phase 2**: Create apps/api and apps/web packages
3. **Phase 3**: Create packages/shared for common utilities

### Success Criteria
- All packages can be installed with single `pnpm install`
- Cross-package imports work correctly
- Build times are reasonable (<60s for full build)
- CI pipeline can test all packages efficiently

---

## 📊 Consequences

### Positive Consequences
- ✅ Simplified dependency management and version alignment
- ✅ Easier cross-package refactoring and testing
- ✅ Shared tooling configuration (ESLint, TypeScript, etc.)
- ✅ Coordinated releases and versioning
- ✅ Better developer experience with unified commands

### Negative Consequences
- ❌ Slightly more complex CI/CD setup initially
- ❌ Larger repository size over time
- ❌ Potential for tighter coupling between packages

### Neutral Consequences
- ⚪ Learning curve for team members unfamiliar with monorepos
- ⚪ Need to establish conventions for package organization

---

## 🔄 Follow-up Actions

### Immediate Actions
- [x] Create pnpm-workspace.yaml configuration
- [x] Set up apps/ and packages/ directory structure
- [x] Configure root package.json with workspace scripts
- [x] Update CI configuration for monorepo builds

### Future Considerations
- Monitor repository size and build performance
- Consider splitting if complexity becomes unmanageable
- Evaluate tools like Turborepo for build optimization

### Review Schedule
- **First Review**: 2025-08-25 - Assess developer experience and build performance
- **Regular Reviews**: Quarterly - Monitor for scaling issues

---

## 📚 References

### Related ADRs
- [ADR-002: TypeScript-First Development](./adr-002-typescript.mdx)

### External References
- [pnpm Workspaces Documentation](https://pnpm.io/workspaces)
- [Monorepo Best Practices](https://monorepo.tools/)

### Internal Documentation
- [Repository Structure](../00_structure.mdx)
- [Milestone M0](../milestones/milestone-M0.mdx)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-05-25 | Initial decision record | nitishMehrotra |

<Callout emoji="📝">
This ADR documents a foundational architectural decision that affects all subsequent development.
</Callout>
