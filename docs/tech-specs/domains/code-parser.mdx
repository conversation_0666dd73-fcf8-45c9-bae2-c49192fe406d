---
title: "Code Parser Domain"
description: "Static code analysis and knowledge graph generation from source files"
version: "1.0.0"
status: "Active"
created: "2025-01-27"
updated: "2025-01-27"
authors: ["WorkflowMapper Team"]
tags: ["code-analysis", "tree-sitter", "knowledge-graph", "static-analysis"]
---

# Code Parser Domain

## Overview

The Code Parser domain provides static analysis capabilities for source code files, extracting function definitions and call relationships to build knowledge graphs. This domain enables automated discovery of code structure and dependencies across Python and JavaScript codebases.

## Core Capabilities

### Function Extraction
- **Python Functions**: `def` statements with parameters, docstrings, and metadata
- **JavaScript Functions**: Function declarations, expressions, arrow functions, and class methods
- **Metadata Capture**: Function signatures, line numbers, file locations, and language identification

### Call Graph Analysis
- **Direct Calls**: Same-file function invocations with line-level precision
- **Relationship Mapping**: Source-to-target function call edges with confidence scoring
- **Deterministic IDs**: Stable function and edge identifiers for consistent graph merging

### Knowledge Graph Integration
- **JSON-LD Output**: Structured data compatible with existing knowledge graph formats
- **YAML Export**: Human-readable format for documentation and review
- **Graph Merging**: Seamless integration with specification-based knowledge graphs

## Architecture

### Core Components

#### code-parser-lib Package
- **parseFile.ts**: Tree-sitter based source file analysis
- **callGraph.ts**: Call relationship extraction and edge generation
- **index.ts**: Public API for external consumption

#### CLI Integration
- **kg-cli Enhancement**: `--code` flag for directory scanning
- **Language Filtering**: `--languages` parameter for selective parsing
- **Dry-run Mode**: Validation and statistics without file modification

### Technology Stack

```yaml
parser: tree-sitter@0.22.3
grammars:
  python: tree-sitter-python@0.23.6
  javascript: tree-sitter-javascript@0.23.1
runtime: node@20.11.0
package_manager: pnpm@8.15.4
testing: jest@29.7.0
build: tsup@8.0.2
```

### Data Models

#### Function Node
```typescript
interface ParsedFunction {
  id: string;                    // function:<hash(filepath+name+startLine)>
  name: string;                  // Function name
  signature: string;             // Function signature with parameters
  file: string;                  // Relative file path
  lang: 'python' | 'javascript'; // Source language
  line_start: number;            // Starting line number
  line_end: number;              // Ending line number
}
```

#### Call Graph Edge
```typescript
interface CallGraphEdge {
  '@id': string;                 // Unique edge identifier
  '@type': 'workflow_calls';     // Edge type for knowledge graph
  source: string;                // Source function @id
  target: string;                // Target function @id
  call_type: 'direct';           // Call type (direct only for M1)
  confidence: number;            // Confidence score (1.0 for direct calls)
  file: string;                  // File where call occurs
  line: number;                  // Line number of call
}
```

## Usage Patterns

### CLI Usage
```bash
# Basic code parsing
pnpm run build-kg -- --code src/ docs/tech-specs

# Dry-run with statistics
pnpm run build-kg -- --code tests/fixtures --dry-run docs/tech-specs

# Language-specific parsing
pnpm run build-kg -- --code src/ --languages py docs/tech-specs
```

### Programmatic Usage
```typescript
import { parseCodeDirectory, extractCallGraph } from '@workflow-mapper/code-parser-lib';

// Parse directory
const parseResult = parseCodeDirectory('./src', ['py', 'js']);

// Extract call graph
const callGraph = extractCallGraph(parseResult);

// Access results
console.log(`Functions: ${parseResult.functions.length}`);
console.log(`Edges: ${callGraph.edges.length}`);
```

## Quality Standards

### Test Coverage
- **Unit Tests**: ≥80% statement coverage
- **Integration Tests**: CLI and graph merging validation
- **Acceptance Tests**: End-to-end workflow verification

### Performance Benchmarks
- **Response Time**: <200ms per file for typical source files
- **Memory Usage**: <100MB for directories with <1000 files
- **Accuracy**: >95% function detection rate for well-formed code

### Error Handling
- **Parse Errors**: Graceful handling with detailed error reporting
- **Unsupported Files**: Clear messaging for non-code files
- **Malformed Code**: Partial parsing with error context

## Integration Points

### Knowledge Graph System
- **Spec Parser**: Seamless integration with MDX specification parsing
- **Graph Merging**: Unified JSON-LD output combining specs and code
- **CLI Workflow**: Single command for complete knowledge graph generation

### CI/CD Pipeline
- **GitHub Actions**: Automated validation on code changes
- **Quality Gates**: Test coverage and parsing accuracy requirements
- **Deployment**: Automated package publishing and version management

## Limitations & Constraints

### Current Scope (M1.1)
- **Languages**: Python and JavaScript only
- **Call Analysis**: Same-file direct calls only
- **Dynamic Analysis**: No runtime behavior analysis
- **Cross-file Calls**: Not supported in initial version

### Future Enhancements (M1.2+)
- **Additional Languages**: TypeScript, Go, Rust support
- **Cross-file Analysis**: Import/export relationship mapping
- **Dynamic Calls**: Method dispatch and callback analysis
- **Performance Optimization**: Incremental parsing and caching

## Monitoring & Observability

### Metrics
- **Parse Success Rate**: Percentage of files successfully parsed
- **Function Detection Rate**: Functions found vs. expected
- **Edge Accuracy**: Call relationships correctly identified
- **Performance**: Parse time per file and directory

### Logging
- **Parse Events**: Function and call discovery logging
- **Error Events**: Detailed error context and recovery actions
- **Performance Events**: Timing and resource usage tracking

## Security Considerations

### Input Validation
- **File Path Sanitization**: Prevention of directory traversal attacks
- **Content Limits**: Maximum file size and directory depth restrictions
- **Language Detection**: Secure file type identification

### Output Security
- **Data Sanitization**: Safe handling of extracted code content
- **Access Control**: Appropriate permissions for generated files
- **Audit Trail**: Logging of all parsing operations

## Compliance & Standards

### Code Quality
- **ESLint**: Strict TypeScript linting with custom rules
- **Prettier**: Consistent code formatting
- **Jest**: Comprehensive test coverage with quality gates

### Documentation
- **API Documentation**: Complete TypeScript interface documentation
- **Usage Examples**: Comprehensive CLI and programmatic examples
- **Architecture Diagrams**: Visual representation of system components

---

## Change Log

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-01-27 | Initial domain specification | WorkflowMapper Team |
