---
title: Knowledge Graph Audit Domain
description: Coverage metrics, confidence scoring, and unknown edge detection for knowledge graph validation
created: 2025-06-03
version: 1.0.0
status: Active
tags: [domain, audit, coverage, confidence]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🔍">
<strong>Domain:</strong> Knowledge Graph Audit provides comprehensive coverage analysis, confidence scoring, and unknown edge detection to ensure knowledge graph quality and completeness.
</Callout>

---

## 🎯 Domain Overview

The Knowledge Graph Audit domain encompasses all functionality related to analyzing, validating, and reporting on knowledge graph quality. This includes coverage calculation, confidence scoring, unknown edge detection, and automated audit reporting.

### Core Responsibilities
- **Coverage Analysis**: Calculate implementation coverage for milestones and components
- **Confidence Scoring**: Assess confidence levels based on verification timestamps
- **Unknown Edge Detection**: Identify missing targets and stale specifications
- **Audit Reporting**: Generate comprehensive audit reports with metrics and validation

---

## 📊 Coverage Calculation

### Coverage Formula
```typescript
coverage = implementedComponents / totalComponents
```

### Component Counting
- **Total Components**: Extracted from milestone specifications (tasks, deliverables)
- **Implemented Components**: Unique components with @implements annotations
- **Stale Components**: Components with confidence ≤ 0.2

### Milestone Coverage Thresholds
- **🟢 Green**: ≥ 75% coverage (healthy implementation)
- **🟡 Yellow**: 50-74% coverage (needs attention)
- **🔴 Red**: < 50% coverage (critical gap)

---

## 🎯 Confidence Scoring

### Confidence Formula
```typescript
// Time-based confidence decay
daysSinceVerified = daysBetween(lastVerified, new Date())
confidence = daysSinceVerified > 30 ? 0.8 : 1.0
```

### Confidence Levels
- **1.0**: Recently verified (≤ 30 days)
- **0.8**: Aging verification (> 30 days)
- **0.2**: Unknown edges (missing targets, stale specs)
- **0.0**: No implementation found

### Confidence Aggregation
- **Milestone Confidence**: Average of all implements edges for that milestone
- **Overall Confidence**: Weighted average across all milestones

---

## 🔍 Unknown Edge Detection

### Detection Rules

#### Workflow Calls
- **Rule**: Target function must exist in code or knowledge graph
- **Detection**: Cross-reference workflow_calls edges with function nodes
- **Confidence**: 0.2 for missing targets
- **Reason**: `missing_target`

#### Implements Edges
- **Rule 1**: Milestone specification must exist
- **Rule 2**: Component must exist within milestone
- **Detection**: Parse milestone content for component references
- **Confidence**: 0.2 for stale specs or missing components
- **Reasons**: `stale_spec`, `missing_target`

### Unknown Edge Thresholds
- **Warning**: > 5 unknown edges
- **Failure**: > 10 unknown edges
- **Critical**: > 20% increase vs previous audit

---

## 📋 Audit Report Structure

### Report Schema
```typescript
interface AuditReport {
  summary: {
    generatedAt: string;           // ISO timestamp
    edgeTotals: Record<string, number>;  // Edge counts by type
    gitRef: string;                // Git reference
    filesScanned: number;          // Files processed
  };
  milestones: Array<{
    id: string;                    // Milestone identifier
    coverage: number;              // 0.0 - 1.0
    confidence: number;            // 0.0 - 1.0
    lastVerified: string;          // ISO timestamp
    components: {
      total: number;               // Total components
      implemented: number;         // Implemented count
      stale: number;              // Stale implementations
    };
  }>;
  unknownEdges: Array<{
    type: 'workflow_calls' | 'implements';
    source: string;                // Source identifier
    target: string;                // Target identifier
    confidence: number;            // Always 0.2
    reason: 'missing_target' | 'stale_spec' | 'parse_error';
    filePath?: string;             // Source file path
    lineNumber?: number;           // Source line number
  }>;
  performance: {
    durationMs: number;            // Execution time
    filesProcessed: number;        // Files analyzed
    edgesAnalyzed: number;         // Edges processed
    cacheHits?: number;            // Cache utilization
  };
}
```

### Output Formats
- **JSON**: Machine-readable audit report (`kg-audit.json`)
- **Pretty**: Human-readable colored console output
- **Both**: Generate both JSON and console output

---

## 🚨 Thresholds & Exit Codes

### Coverage Thresholds
- **Default Threshold**: 50% minimum coverage
- **Configurable**: `--fail-under` parameter
- **Enforcement**: CI fails if any milestone below threshold

### Exit Codes
- **0**: Success (all thresholds met)
- **1**: General error (file not found, parse error)
- **61**: Coverage threshold breach
- **62**: Unknown edge cap exceeded

### Performance Requirements
- **Target**: ≤ 60 seconds for ≤ 2000 files
- **Typical**: 20-50ms for small repositories
- **Monitoring**: Performance metrics included in audit report

---

## 🔧 Implementation Architecture

### Core Components
- **kg-audit-lib**: Core audit logic and algorithms
- **audit-kg CLI**: Command-line interface for audit execution
- **Schema Validation**: JSON Schema validation for audit reports
- **CI Integration**: GitHub Actions workflow for automated auditing

### Data Flow
1. **Input**: Load `kg.jsonld` knowledge graph
2. **Analysis**: Calculate coverage and detect unknown edges
3. **Scoring**: Apply confidence scoring algorithms
4. **Validation**: Validate report against JSON schema
5. **Output**: Generate JSON report and console summary

### Integration Points
- **kg-sync-lib**: Reuses coverage calculation and confidence scoring
- **kg-cli**: Follows established CLI patterns and error handling
- **CI/CD**: Integrates with existing GitHub Actions workflows

---

## 📈 Quality Metrics

### Audit Quality Indicators
- **Coverage Completeness**: All milestones have coverage data
- **Confidence Accuracy**: Confidence scores reflect actual verification status
- **Unknown Edge Detection**: All missing targets and stale specs identified
- **Performance**: Audit completes within performance requirements

### Validation Criteria
- **Schema Compliance**: Audit report validates against JSON schema
- **Data Integrity**: Coverage calculations are mathematically correct
- **Edge Detection**: Unknown edges are properly categorized and reasoned
- **Temporal Accuracy**: Timestamps and verification dates are current

---

## 🔄 Maintenance & Evolution

### Regular Maintenance
- **Schema Updates**: Evolve schema as audit requirements change
- **Threshold Tuning**: Adjust coverage and unknown edge thresholds based on usage
- **Performance Optimization**: Monitor and optimize audit execution time
- **Algorithm Refinement**: Improve unknown edge detection accuracy

### Future Enhancements
- **Trend Analysis**: Track coverage and confidence trends over time
- **Predictive Scoring**: Predict future confidence decay
- **Advanced Detection**: More sophisticated unknown edge detection
- **Integration Expansion**: Support for additional knowledge graph sources

---

**Domain Status**: ✅ **ACTIVE** - Fully implemented and production-ready with comprehensive audit capabilities
