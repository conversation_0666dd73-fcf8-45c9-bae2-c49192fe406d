---
title: "Knowledge Graph Sync Domain"
description: "Bidirectional synchronization between code annotations and milestone specifications"
version: "1.2.0"
status: "Active"
created: "2025-06-01"
updated: "2025-06-01"
authors: ["WorkflowMapper Team"]
tags: ["bidirectional-sync", "annotations", "git-diff", "knowledge-graph", "incremental-updates"]
---

# Knowledge Graph Sync Domain

## Overview

The Knowledge Graph Sync domain provides bidirectional synchronization capabilities between source code annotations and milestone specifications. This domain enables incremental knowledge graph updates based on git diff detection, maintaining confidence scores and coverage metrics for milestone implementation tracking.

## Core Capabilities

### Annotation Parsing
- **JSDoc/TSDoc Support**: Parse `@implements milestone-ID#Component` annotations
- **Multi-language**: TypeScript, JavaScript, and Python comment formats
- **Validation**: Comprehensive format validation with detailed error reporting
- **Context Awareness**: Ensures annotations are attached to functions, classes, or methods

### Git Diff Integration
- **Incremental Detection**: Identify changed files since specified commit/branch
- **Performance Optimization**: 90%+ improvement over full repository scans
- **Edge Case Handling**: Merge conflicts, binary files, file renames, large diffs
- **Source Filtering**: Focus on relevant source code files only

### Knowledge Graph Updates
- **Bidirectional Sync**: Code ↔ specifications synchronization
- **Confidence Scoring**: 5-level confidence system with time-based degradation
- **Coverage Metrics**: Milestone implementation coverage calculation
- **Stale Detection**: Automatic detection and marking of outdated relationships

## Architecture

### Core Components

#### kg-sync-lib Package
- **diffGit.ts**: Git diff detection using simple-git integration
- **parseAnnotations.ts**: JSDoc annotation parsing with comment-parser
- **updateGraph.ts**: Incremental graph updates with confidence scoring
- **extractComponents.ts**: Component extraction from milestone specifications
- **confidence.ts**: Confidence calculation and coverage metrics
- **sync.ts**: Main orchestration and error handling

#### CLI Integration
- **sync-kg Command**: `pnpm run sync-kg -- --since <gitRef>`
- **Parameters**: `--since`, `--dry-run`, `--threshold`, `--output-dir`
- **Exit Codes**: 0 (success), 60 (coverage breach), 70 (parse error), 1 (error)
- **Reporting**: Coverage tables, changes reports, performance metrics

### Technology Stack

```yaml
git_integration: simple-git@3.22.0
comment_parsing: comment-parser@1.4.0
pattern_matching: regexparam@2.0.0
runtime: node@20.11.0
package_manager: pnpm@8.15.4
testing: jest@29.7.0
build: tsup@8.0.2
```

### Data Models

#### Annotation Structure
```typescript
interface Annotation {
  milestoneId: string;        // e.g., "M1.2"
  componentName: string;      // e.g., "AnnotationParser"
  functionName: string;       // e.g., "parseAnnotations"
  filePath: string;          // e.g., "src/parser.ts"
  lineNumber: number;        // Line where annotation appears
  confidence: number;        // 0.1 to 1.0 confidence score
  lastVerified: string;      // ISO timestamp
  errors: ParseError[];      // Validation errors if any
}
```

#### Coverage Metrics
```typescript
interface MilestoneCoverage {
  milestoneId: string;
  totalComponents: number;
  implementedComponents: number;
  coverage: number;           // 0.0 to 1.0
  confidence: number;         // Average confidence of all edges
  lastUpdated: string;        // ISO timestamp
}
```

## Usage Patterns

### CLI Usage
```bash
# Basic incremental sync
pnpm run sync-kg -- --since origin/main docs/tech-specs

# Dry-run validation
pnpm run sync-kg -- --since HEAD~1 --dry-run docs/tech-specs

# Custom coverage threshold
pnpm run sync-kg -- --since HEAD~5 --threshold 0.7 docs/tech-specs

# Verbose output with performance metrics
pnpm run sync-kg -- --since origin/main --verbose docs/tech-specs
```

### Programmatic Usage
```typescript
import { syncKnowledgeGraph } from '@workflow-mapper/kg-sync-lib';

// Basic sync operation
const result = await syncKnowledgeGraph('./docs/tech-specs', {
  since: 'origin/main',
  dryRun: false,
  outputDir: '.',
  verbose: true
});

// Check results
console.log(`Exit code: ${result.exitCode}`);
console.log(`Coverage: ${result.coverageMetrics.length} milestones`);
console.log(`Performance: ${result.performance.filesProcessed} files processed`);
```

### Annotation Examples
```typescript
/**
 * @implements milestone-M1.2#AnnotationParser
 */
export function parseAnnotations(content: string, filePath: string): Annotation[] {
  // Implementation
}

/**
 * @implements milestone-M1.2#GitDiffDetector
 */
class GitDiffDetector {
  /**
   * @implements milestone-M1.2#DiffAnalysis
   */
  async detectChanges(since: string): Promise<DiffResult> {
    // Implementation
  }
}
```

## Quality Standards

### Performance Requirements
- **Incremental Efficiency**: 90%+ improvement over full repository scans
- **Response Time**: <500ms for typical incremental operations
- **Memory Usage**: <100MB for repositories with 1000+ files
- **Scalability**: Linear scaling with number of changed files

### Accuracy Standards
- **Annotation Parsing**: 99%+ accuracy on test corpus
- **Confidence Scoring**: Proper 5-level confidence calculation
- **Coverage Calculation**: Accurate milestone coverage with threshold enforcement
- **Error Handling**: Comprehensive error reporting with file context

### Test Coverage
- **Unit Tests**: ≥95% coverage for all core functions
- **Integration Tests**: End-to-end CLI and workflow scenarios
- **Performance Tests**: Benchmarking and regression detection
- **Edge Cases**: File renames, merge conflicts, malformed annotations

## Integration Points

### CI/CD Pipeline
- **GitHub Actions**: Automated validation on pull requests
- **Coverage Validation**: Fails if milestone coverage < 50%
- **Quality Gates**: Test coverage and parsing accuracy requirements
- **Performance Monitoring**: Tracks sync operation performance

### Knowledge Graph System
- **Spec Parser**: Integration with MDX specification parsing
- **Graph Merging**: Unified JSON-LD output combining specs and code
- **CLI Workflow**: Single command for complete knowledge graph updates
- **Artifact Generation**: kg.jsonld, kg.yaml, kg-changes.json outputs

## Confidence Scoring System

### Confidence Levels
- **1.0**: Valid annotation, function exists, recently verified
- **0.8**: Valid annotation, function exists, not recently verified (>30 days)
- **0.5**: Annotation exists but has validation warnings
- **0.2**: Annotation removed but function still exists (stale)
- **0.1**: Function deleted but edge preserved for history (stale)

### Coverage Calculation
```typescript
// Effective coverage considers confidence levels
const effectiveCoverage = (
  fullImplementations.length + 
  partialImplementations.length * 0.5 + 
  staleImplementations.length * 0.3
) / totalComponents;
```

## Limitations & Constraints

### Current Scope (M1.2)
- **Languages**: TypeScript, JavaScript, Python comment formats
- **Git Integration**: Requires git repository with proper history
- **Annotation Format**: Specific JSDoc/TSDoc format required
- **File Types**: Source code files only (filtered by extension)

### Performance Considerations
- **Large Repositories**: May require chunking for repositories with 10,000+ files
- **Binary Files**: Automatically skipped but counted in statistics
- **Network Operations**: Git operations may be slow on large repositories
- **Memory Usage**: Scales with number of annotations and graph size

## Security Considerations

### Input Validation
- **File Path Sanitization**: Prevention of directory traversal attacks
- **Content Limits**: Maximum file size and annotation count restrictions
- **Git Reference Validation**: Secure handling of git references and commits

### Output Security
- **Data Sanitization**: Safe handling of extracted annotation content
- **Access Control**: Appropriate permissions for generated files
- **Audit Trail**: Logging of all sync operations and changes

## Future Enhancements

### Planned Features (M2+)
- **Cross-language Support**: Additional programming languages
- **Real-time Sync**: File system watching for immediate updates
- **Conflict Resolution**: Advanced merge conflict handling
- **Performance Optimization**: Parallel processing and caching

### Integration Opportunities
- **IDE Extensions**: Real-time annotation validation in editors
- **Dashboard Integration**: Visual coverage and confidence reporting
- **Notification Systems**: Alerts for coverage threshold breaches
- **API Endpoints**: REST API for external system integration

---

**Domain Owner**: Development Team  
**Review Frequency**: After each milestone or when significant issues identified  
**Last Updated**: 2025-06-01
