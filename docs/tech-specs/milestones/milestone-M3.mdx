---
title: Milestone M3 — Specification & Documentation Generators
description: Generate OpenAPI/AsyncAPI specs, integration docs, and user manuals directly from the KG.
created: 2025-05-30
version: 0.1.0
status: Draft
tags: [milestone]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="📄">
<strong>Goal:</strong> From the enriched KG, emit three human-consumable artefact families:<br/>
1️⃣ OpenAPI / AsyncAPI specs, 2️⃣ Integration docs (per external service), 3️⃣ User manual skeletons.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
yaml: "2.3.2"
openapi4j: "1.1.7"          # spec validation
handlebars: "4.7.8"         # doc templates
markdown-it: "14.0.0"
jest: "29.7.0"
```

---

## 🎯 Definition of Done

1. **Generator CLI**: `pnpm run generate-docs` produces:
   - `specs/openapi/<service>.yaml`
   - `specs/asyncapi/<queue>.yaml`
   - `docs/generated/integration/<service>.md`
   - `docs/generated/user-manual/<milestone>.md`
2. **Validation**: All generated OpenAPI/AsyncAPI files pass openapi4j validation.
3. **Filtering**: CLI supports `--filter <milestone|service>` to emit subset.
4. **CI**: CI job `doc-gen` runs on main nightly; fails if validation fails or files drift.
5. **Coverage**: Unit-test coverage ≥ 80% for generator code.
6. **Performance**: End-to-end `pnpm run generate-docs` completes in ≤ 90 seconds on a KG with ≤ 20,000 nodes (checked in CI).

---

## 📦 Deliverables

| Artefact / Path                        | Content                                                      |
|----------------------------------------|--------------------------------------------------------------|
| code/packages/doc-gen-lib/             | builders: openapi.ts, asyncapi.ts, markdown.ts               |
| code/packages/kg-cli/                  | new command generate-docs.ts                                 |
| specs/openapi/                         | Generated API specs (committed)                              |
| specs/asyncapi/                        | Generated async specs                                        |
| docs/generated/                        | Integration docs & user manuals                              |
| .github/workflows/doc-gen.yml          | CI + nightly validation                                      |
| docs/tech-specs/domains/doc-gen.mdx    | Domain doc describing template fields                        |
| code/packages/doc-gen-lib/templates/   | Handlebars templates: openapi.hbs, asyncapi.hbs, integration.hbs, manual.hbs, _partials/param.hbs |

---

## 🗂 Directory Additions

```text
specs/
├─ openapi/        # YAML
└─ asyncapi/
docs/generated/
├─ integration/
└─ user-manual/
code/packages/doc-gen-lib/
```

---

## 🧠 Key Decisions

| Topic                      | Decision                                                               | Rationale |
|----------------------------|------------------------------------------------------------------------|-----------|
| OpenAPI version            | 3.1 (YAML)                                                            | JSON Schema compatibility. |
| Tag naming convention      | Tag = `<ComponentType>:<ComponentName>` – e.g. `service:user`, `db:neo4j`. Paths inherit first tag. | Predictable grouping in docs & client-gen. |
| Template engine            | Handlebars `.hbs` in `templates/`, partials under `_partials/`.       | Non-tech contributors can edit. |
| Generated files committed? | **Yes** – committed under `specs/` and `docs/generated/`.             | Review in PR diff; publishable. |
| Performance guardrail      | Full generation must finish **≤ 90 s** on KG ≤ 20 K nodes.            | Keeps CI fast. |

---

## 🛠️ Technical Specifications

### 📝 Generation Algorithm

**Input**: Knowledge Graph (KG) JSON-LD
**Output**: OpenAPI/AsyncAPI YAML, Markdown docs, user manual

**Steps:**
1. **Extract Service/Queue/Component Nodes**: Parse KG for relevant nodes.
2. **Template Rendering**: Use Handlebars templates for each artefact type.
3. **Validation**: Validate OpenAPI/AsyncAPI output with openapi4j.
4. **Write Outputs**: Overwrite existing files in output directories.
5. **Performance**: Ensure generation completes within 90 seconds for large KGs.

**Error Handling:**
- Fail CI if validation fails or files drift.
- Log and skip artefacts with missing required data.

### 📦 Output Interfaces

```typescript
interface GeneratedArtefact {
  path: string;
  content: string;
  type: 'openapi' | 'asyncapi' | 'integration-doc' | 'user-manual';
  validated: boolean;
  errors?: string[];
}
```

---

## 🔨 Task Breakdown

| #   | Branch                | Task                                         | Owner |
|-----|-----------------------|----------------------------------------------|-------|
| 01  | m3/doc-gen-lib        | Scaffold library (tsconfig, jest)            | BE    |
| 02  | m3/openapi-builder    | Build OpenAPI YAML from KG service nodes     | BE    |
| 03  | m3/async-builder      | Build AsyncAPI YAML                          | BE    |
| 04  | m3/markdown-ints      | Generate integration docs via Handlebars     | BE    |
| 05  | m3/manual-builder     | Generate user-manual skeleton per milestone  | BE    |
| 06  | m3/cli                | Add generate-docs.ts command                 | BE    |
| 07  | m3/tests              | Jest tests (validate specs, snapshot docs)   | BE    |
| 08  | m3/ci                 | Add doc-gen.yml workflow                     | DevOps|
| 09  | m3/domain-doc         | Write doc-gen.mdx domain spec                | PM    |
| 10  | m3/spec-quality       | Run spec-lint; approve spec                  | PM    |
| 11  | m3/final-tag          | Merge & tag doc-gen-v3.0.0                   | Lead  |

---

## 🤖 CLI Specification (generate-docs)

**Usage:**

```bash
pnpm run generate-docs [--filter openapi|asyncapi|docs] [--milestone <id>] [--service <name>]
```

- Defaults: all artefacts.
- Writes files, overwriting existing.
- Exit 0 on success; 71 on validation error.

---

## 🤖 CI Workflow (.github/workflows/doc-gen.yml)

```yaml
name: Doc Gen
on:
  push:
    branches: [main]
  schedule:
    - cron: '0 2 * * *'

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run generate-docs
      - run: git diff --exit-code   # fails if generator introduced changes on PR
```

---

## 🧪 Acceptance Tests

### 1️⃣ OpenAPI generation

```bash
pnpm run generate-docs --filter openapi --service user-service
openapi-cli validate specs/openapi/user-service.yaml
```

### 2️⃣ Manual skeleton

```bash
test -f docs/generated/user-manual/milestone-M1.md
grep -q "## Requirements" docs/generated/user-manual/milestone-M1.md
```

### 3️⃣ Coverage

```bash
pnpm --filter doc-gen-lib test --coverage
# ≥ 80 %
```

---

## ✅ Success Criteria

- [ ] **SC-1:** Spec + CLI generate OpenAPI, AsyncAPI, integration MD, user manual.
- [ ] **SC-2:** All generated specs pass validation.
- [ ] **SC-3:** CI doc-gen job passes / blocks PR if files drift.
- [ ] **SC-4:** Tests ≥ 80 % coverage.
- [ ] **SC-5:** Spec passes spec-lint.
- [ ] **SC-6:** openapi4j validation passes for every file in specs/openapi.
- [ ] **SC-7:** CI asserts runtime ≤ 90 s (fails if slower).
- [ ] **SC-8:** Merge → tag doc-gen-v3.0.0

---

## 🔄 Document History

| Version | Date       | Changes                                   | Author              |
|---------|------------|-------------------------------------------|---------------------|
| 0.1.0   | 2025-05-30 | Initial milestone specification           | nitishMehrotra      |
| 0.2.0   | 2025-06-02 | Refactored for structure, clarity, parity | nitishMehrotra      |
