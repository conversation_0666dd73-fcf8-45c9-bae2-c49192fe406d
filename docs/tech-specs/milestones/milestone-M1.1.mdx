---
title: Milestone M1 — Static Code Parser & Graph Augmenter
description: Parse Python, JavaScript, and TypeScript source files with Tree-sitter, extract functions & call-graph, and merge results into the existing KG (kg.jsonld / kg.yaml).
created: 2025-05-29
version: 0.2.0
status: Complete
tags: [milestone]
authors: [WorkflowMapper Team]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🛠️">
<strong>Scope:</strong> “Code → Graph” direction.<br/>
Output must enrich <code>kg.jsonld</code> / <code>kg.yaml</code> with new <code>function</code> nodes and <code>workflow_calls</code> edges, without breaking M0.2 CLI.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
tree-sitter-cli: "0.25.4"
tree-sitter: "0.22.3"           # Node bindings
tree-sitter-python: "0.23.6"
tree-sitter-javascript: "0.23.1"
tree-sitter-typescript: "0.23.0"  # Added for TypeScript support
jest: "29.7.0"
esbuild-register: "3.4.2"        # for ts-node-like import in CLI tests
```

---

## 🎯 Definition of Done

1. `pnpm run build-kg -- --code src/` scans all `.py` & `.js` files under `src/` (recursively) and updates `kg.jsonld` + `kg.yaml` (preserve milestone nodes).
2. Each discovered function becomes a function node with properties: `name`, `signature`, `file`, `lang`, `line_start`, `line_end`.
3. Each direct call site is added as `workflow_calls` edge with metadata `{call_type: 'direct', confidence: 1.0}`.
4. CLI supports `--code` and `--dry-run` together (prints stats, no write).
5. CI job `code-parse` passes (build-kg dry-run on a fixture repo).
6. Unit-test coverage ≥ 80% for new packages.

---

## 📦 Deliverables

| Path / Artefact                        | Must contain / do                                 |
|----------------------------------------|---------------------------------------------------|
| code/packages/code-parser-lib/         | parseFile.ts, callGraph.ts, tests                 |
| code/packages/kg-cli/                  | enhanced CLI: new --code flag, merges graph       |
| tests/fixtures/python/hello.py         | Simple test file (≥1 function)                    |
| tests/fixtures/javascript/hello.js     | Simple test file (≥1 function)                    |
| tests/fixtures/typescript/hello.ts     | Simple test file (≥1 function) - TypeScript support |
| .github/workflows/code-parse.yml       | CI job: build-kg dry-run on fixtures              |
| docs/tech-specs/domains/code-parser.mdx| Domain spec (how parsing works, node props)        |

---

## 🗂 Directory Layout (after M1)

```text
code/
└─ packages/
   ├─ code-parser-lib/           # NEW
   │   ├─ src/
   │   │   ├─ parseFile.ts
   │   │   ├─ callGraph.ts
   │   │   └─ index.ts
   │   ├─ tests/
   │   └─ package.json
   └─ kg-cli/                    # enhanced
```

---

## 🧠 Key Decisions

| Topic                | Decision                                              | Rationale                                         |
|----------------------|------------------------------------------------------|---------------------------------------------------|
| Languages in scope   | Python (.py), JavaScript (.js, .mjs, .cjs), TypeScript (.ts, .tsx) | Most common in repo, comprehensive Tree-sitter support. |
| Edge granularity     | Only direct, same-file calls for M1; no dynamic dispatch. | Simpler first slice; indirect edges will come in M1.1. |
| Graph merge strategy | Read existing kg.jsonld → append new nodes/edges → overwrite. | Idempotent; dry-run uses in-memory merge only.     |
| Deterministic IDs    | function:<hash(filepath+name+startLine)>             | Prevent dupes on re-parse; stable across branches. |

---

## 🔨 Task Breakdown

| #   | Branch name           | Task                                         | Owner |
|-----|-----------------------|----------------------------------------------|-------|
| 01  | m1/parser-lib-init    | Scaffold code-parser-lib (tsconfig, jest)    | BE    |
| 02  | m1/tree-sitter-setup  | Install & compile grammars (Python, JS, TS)  | BE    |
| 03  | m1/parse-file         | Implement parseFile.ts → returns func list   | BE    |
| 04  | m1/call-graph         | Implement callGraph.ts → returns edge list   | BE    |
| 05  | m1/cli-flag           | Add --code flag to kg-cli; integrate parser-lib | BE |
| 06  | m1/tests-parser       | Jest unit tests on fixtures (functions & calls) | BE |
| 07  | m1/tests-cli          | Integration test: build-kg dry-run merges graph | BE |
| 08  | m1/ci                 | Add workflow code-parse.yml (dry-run on fixtures) | DevOps |
| 09  | m1/domain-doc         | Write code-parser.mdx domain documentation   | PM    |
| 10  | m1/spec-quality       | Run spec-lint; set this spec status → Approved | PM |
| 11  | m1/final-tag          | Merge & tag code-parser-v1.0.0               | Lead  |

<Callout emoji="🗂">One PR per task. Reviewers tick acceptance hint in PR description.</Callout>

---

## 🤖 CLI Specification (enhanced)

**New flags**

| Flag               | Type           | Description                                         |
|--------------------|----------------|-----------------------------------------------------|
| --code <dir>       | path           | Directory to scan for .py, .js, .ts files.         |
| --languages py,js,ts | list (optional)| Comma-separated whitelist; default py,js,ts.       |
| --dry-run          | boolean        | Parse & merge in-memory; print summary.             |

**Stats printed on dry-run**

```yaml
Functions added:  42
workflow_calls added:  87
Files scanned:    15
Skipped (parse error): 1
Exit status: 0 success, 1 parse error fatal.
```

---

## 🤖 CI Pipeline

```yaml
name: Code Parse
on: [push, pull_request]

jobs:
  code-parse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run build-kg -- --code tests/fixtures --dry-run
# (No graph files committed during PRs.)
```

---

## 🧪 Acceptance Tests

### 1️⃣ Unit parse
```bash
node -e "require('./code/packages/code-parser-lib').parseFile('tests/fixtures/python/hello.py')" \
  | jq '.[0].name=="add"'
```

### 2️⃣ CLI dry-run
```bash
pnpm run build-kg -- --code tests/fixtures --dry-run
# Expect stats, exit 0, no kg files touched
```

### 3️⃣ Graph merge
```bash
pnpm run build-kg -- --code tests/fixtures
jq '."@graph"[] | select(."@type"=="function")' kg.jsonld | head -1
# returns at least one function node
```

### 4️⃣ Coverage
```bash
pnpm --filter code-parser-lib test --coverage
# coverage ≥ 80 %
```

### 5️⃣ CI green
Both Graph Build (from M0.2) and Code Parse jobs pass on PR → merge.

---

## ✅ Success Criteria

- [ ] **SC-1** CLI dry-run exits 0 with correct stats.
- [ ] **SC-2** Real build adds ≥ 1 function node & ≥ 1 workflow edge.
- [ ] **SC-3** Unit + integration tests green, coverage ≥ 80 %.
- [ ] **SC-4** CI code-parse job passes.
- [ ] **SC-5** Spec passes spec-lint.

When all criteria are green, merge to main, tag `code-parser-v1.0.0`, and open Milestone M1.1.

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 0.1.0 | 2025-05-29 | Initial specification draft | WorkflowMapper Team |
| 0.2.0 | 2025-01-27 | Added TypeScript support, updated to Complete status | WorkflowMapper Team |

