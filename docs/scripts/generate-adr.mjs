#!/usr/bin/env node
/**
 * Generate a new Architectural Decision Record (ADR) from template
 * Usage: node generate-adr.mjs 007 "Database Migration Strategy"
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Parse command line arguments
const [, , adrNumber, adrTitle] = process.argv;

if (!adrNumber || !adrTitle) {
  console.error('Usage: node generate-adr.mjs <number> "<Title>"');
  console.error(
    'Example: node generate-adr.mjs 007 "Database Migration Strategy"'
  );
  process.exit(1);
}

// Validate ADR number format (should be 3 digits)
const paddedNumber = adrNumber.padStart(3, "0");
if (!/^\d{3}$/.test(paddedNumber)) {
  console.error(
    "Error: ADR number should be numeric (will be padded to 3 digits)"
  );
  process.exit(1);
}

// Create short title for filename (kebab-case)
const shortTitle = adrTitle
  .toLowerCase()
  .replace(/[^a-z0-9\s]/g, "")
  .replace(/\s+/g, "-")
  .substring(0, 30); // Limit length

// Paths
const templatePath = path.join(
  __dirname,
  "../tech-specs/process/templates/adr-template.mdx"
);
const outputPath = path.join(
  __dirname,
  `../tech-specs/adrs/adr-${paddedNumber}-${shortTitle}.mdx`
);

// Check if template exists
if (!fs.existsSync(templatePath)) {
  console.error(`Error: Template not found at ${templatePath}`);
  process.exit(1);
}

// Check if ADR already exists
if (fs.existsSync(outputPath)) {
  console.error(`Error: ADR ${paddedNumber} already exists at ${outputPath}`);
  process.exit(1);
}

// Read template
let template = fs.readFileSync(templatePath, "utf8");

// Get current date
const currentDate = new Date().toISOString().split("T")[0];

// Replace placeholders
const replacements = {
  "ADR-XXX": `ADR-${paddedNumber}`,
  "<Decision Title>": adrTitle,
  "<Brief description of the architectural decision>": `Architectural decision record for ${adrTitle}.`,
  "<YYYY-MM-DD>": currentDate,
  "<author>": "nitishMehrotra" // Default author, can be changed
};

// Apply replacements
for (const [placeholder, replacement] of Object.entries(replacements)) {
  template = template.replaceAll(placeholder, replacement);
}

// Write new ADR file
fs.writeFileSync(outputPath, template);

console.log(`✅ Created ADR: ${outputPath}`);
console.log("");
console.log("Next steps:");
console.log("1. Edit the generated file to add specific decision details");
console.log(
  "2. Fill in all sections: Context, Options, Decision, Consequences"
);
console.log("3. Update the adrs/log.mdx index table with this ADR");
console.log('4. Change status from "Proposed" to "Accepted" when approved');
console.log("");
console.log("Add to adrs/log.mdx:");
console.log(
  `| ADR-${paddedNumber} | ${adrTitle} | 🟡 Proposed | ${currentDate} | [\`adr-${paddedNumber}-${shortTitle}.mdx\`](./adrs/adr-${paddedNumber}-${shortTitle}.mdx) |`
);
