#!/usr/bin/env node
/**
 * Validate the docs/tech-specs directory structure
 * Ensures all required files exist and follow naming conventions
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const docsRoot = path.join(__dirname, "..", "tech-specs");

// Expected structure
const expectedStructure = {
  files: ["structure.mdx", "dependencies.mdx", "spec_checklist.mdx"],
  logFiles: ["milestones/log.mdx", "adrs/log.mdx"],
  directories: ["milestones", "domains", "adrs", "process", "guides"],
  templates: [
    "process/templates/milestone-template.mdx",
    "process/templates/domain-template.mdx",
    "process/templates/adr-template.mdx"
  ],
  // Scripts are now in docs/scripts/ not docs/tech-specs/scripts/
  scriptsLocation: "../scripts",
  scripts: [
    "spec-lint.mjs",
    "generate-milestone.mjs",
    "generate-adr.mjs",
    "validate-structure.mjs"
  ]
};

let errors = [];
let warnings = [];

console.log("🔍 Validating docs/tech-specs structure...\n");

// Check required files
console.log("📄 Checking required files...");
expectedStructure.files.forEach((file) => {
  const filePath = path.join(docsRoot, file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    errors.push(`Missing required file: ${file}`);
    console.log(`  ❌ ${file} - MISSING`);
  }
});

// Check log files
console.log("\n📊 Checking log files...");
expectedStructure.logFiles.forEach((file) => {
  const filePath = path.join(docsRoot, file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    errors.push(`Missing log file: ${file}`);
    console.log(`  ❌ ${file} - MISSING`);
  }
});

// Check required directories
console.log("\n📁 Checking required directories...");
expectedStructure.directories.forEach((dir) => {
  const dirPath = path.join(docsRoot, dir);
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    console.log(`  ✅ ${dir}/`);
  } else {
    errors.push(`Missing required directory: ${dir}/`);
    console.log(`  ❌ ${dir}/ - MISSING`);
  }
});

// Check templates
console.log("\n📋 Checking templates...");
expectedStructure.templates.forEach((template) => {
  const templatePath = path.join(docsRoot, template);
  if (fs.existsSync(templatePath)) {
    console.log(`  ✅ ${template}`);
  } else {
    errors.push(`Missing template: ${template}`);
    console.log(`  ❌ ${template} - MISSING`);
  }
});

// Check scripts
console.log("\n🔧 Checking scripts...");
const scriptsDir = path.join(docsRoot, expectedStructure.scriptsLocation);
expectedStructure.scripts.forEach((script) => {
  const scriptPath = path.join(scriptsDir, script);
  if (fs.existsSync(scriptPath)) {
    // Check if executable
    try {
      fs.accessSync(scriptPath, fs.constants.X_OK);
      console.log(`  ✅ ${script} (executable)`);
    } catch {
      warnings.push(`Script not executable: ${script}`);
      console.log(`  ⚠️  ${script} (not executable)`);
    }
  } else {
    errors.push(`Missing script: ${script}`);
    console.log(`  ❌ ${script} - MISSING`);
  }
});

// Check milestone naming convention
console.log("\n🎯 Checking milestone files...");
const milestonesDir = path.join(docsRoot, "milestones");
if (fs.existsSync(milestonesDir)) {
  const milestoneFiles = fs
    .readdirSync(milestonesDir)
    .filter((file) => file.endsWith(".mdx") && file !== "log.mdx"); // Exclude log file

  milestoneFiles.forEach((file) => {
    if (/^milestone-M\d+\.mdx$/.test(file)) {
      console.log(`  ✅ ${file}`);
    } else {
      warnings.push(`Milestone file doesn't follow naming convention: ${file}`);
      console.log(`  ⚠️  ${file} - Should be milestone-M{number}.mdx`);
    }
  });

  if (milestoneFiles.length === 0) {
    console.log("  ℹ️  No milestone files found (only log.mdx exists)");
  }
}

// Check ADR naming convention
console.log("\n📋 Checking ADR files...");
const adrsDir = path.join(docsRoot, "adrs");
if (fs.existsSync(adrsDir)) {
  const adrFiles = fs
    .readdirSync(adrsDir)
    .filter((file) => file.endsWith(".mdx") && file !== "log.mdx"); // Exclude log file

  adrFiles.forEach((file) => {
    if (/^adr-\d{3}-[a-z0-9-]+\.mdx$/.test(file)) {
      console.log(`  ✅ ${file}`);
    } else {
      warnings.push(`ADR file doesn't follow naming convention: ${file}`);
      console.log(`  ⚠️  ${file} - Should be adr-XXX-title.mdx`);
    }
  });

  if (adrFiles.length === 0) {
    console.log("  ℹ️  No ADR files found (only log.mdx exists)");
  }
} else {
  console.log(
    "  ℹ️  ADRs directory not found (this is normal for new projects)"
  );
}

// Check for orphaned files
console.log("\n🔍 Checking for unexpected files...");
const allFiles = fs.readdirSync(docsRoot);
const expectedTopLevel = [
  ...expectedStructure.files,
  ...expectedStructure.directories,
  ".DS_Store" // Allow macOS system file
];

allFiles.forEach((file) => {
  if (!expectedTopLevel.includes(file)) {
    warnings.push(`Unexpected file/directory: ${file}`);
    console.log(`  ⚠️  ${file} - Not in expected structure`);
  }
});

// Summary
console.log("\n📊 Validation Summary");
console.log("=".repeat(50));

if (errors.length === 0 && warnings.length === 0) {
  console.log("🎉 All checks passed! Structure is valid.");
} else {
  if (errors.length > 0) {
    console.log(`❌ ${errors.length} error(s):`);
    errors.forEach((error) => console.log(`   • ${error}`));
  }

  if (warnings.length > 0) {
    console.log(`⚠️  ${warnings.length} warning(s):`);
    warnings.forEach((warning) => console.log(`   • ${warning}`));
  }
}

console.log("\n💡 Helpful commands:");
console.log(
  '  • Generate milestone: node scripts/generate-milestone.mjs M1 "Title"'
);
console.log(
  '  • Generate ADR: node scripts/generate-adr.mjs 007 "Decision Title"'
);
console.log(
  "  • Lint milestone: node scripts/spec-lint.mjs milestones/milestone-M1.mdx"
);
console.log("  • Make script executable: chmod +x scripts/script-name.mjs");

// Exit with error code if there are errors
process.exit(errors.length > 0 ? 1 : 0);
