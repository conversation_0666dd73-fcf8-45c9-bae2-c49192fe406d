#!/bin/bash
set -euo pipefail

echo "🧪 Running TEST milestone acceptance tests..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}Testing: $test_name${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Test 1: Check if source file exists
run_test "Function implementation exists" \
    "test -f 'src/utils/string-utils.ts'"

# Test 2: Check if test file exists
run_test "Test file exists" \
    "test -f 'src/utils/string-utils.test.ts'"

# Test 3: Check if documentation exists
run_test "Documentation exists" \
    "test -f 'docs/utils/string-utils.md'"

# Test 4: Check work log files
run_test "Requirement checklist exists" \
    "test -f 'work-log/milestone-test/requirement-checklist.md'"

run_test "Implementation log exists" \
    "test -f 'work-log/milestone-test/implementation-log.md'"

# Test 5: Run unit tests (if npm test is available)
if command -v npm &> /dev/null; then
    run_test "Unit tests pass" \
        "npm test -- --testPathPattern=string-utils.test.ts --passWithNoTests"
else
    echo -e "${YELLOW}⚠️  SKIPPED: Unit tests (npm not available)${NC}"
fi

# Test 6: TypeScript compilation (if tsc is available)
if command -v npx &> /dev/null && npx tsc --version &> /dev/null; then
    run_test "TypeScript compilation" \
        "npx tsc --noEmit --strict src/utils/string-utils.ts"
else
    echo -e "${YELLOW}⚠️  SKIPPED: TypeScript compilation (tsc not available)${NC}"
fi

# Test 7: Check for JSDoc comments
run_test "JSDoc documentation present" \
    "grep -q '/\*\*' src/utils/string-utils.ts"

# Test 8: Check for export
run_test "Function is exported" \
    "grep -q 'export' src/utils/string-utils.ts"

# Test 9: Check work log has content
run_test "Work log has meaningful content" \
    "test \$(wc -l < work-log/milestone-test/implementation-log.md) -gt 20"

# Test 10: Check requirement checklist completion
run_test "Requirement checklist shows completion" \
    "grep -q 'Ready for Implementation: ✅ Yes' work-log/milestone-test/requirement-checklist.md"

echo -e "\n================================================"
echo -e "📊 ${GREEN}ACCEPTANCE TEST RESULTS${NC}"
echo -e "================================================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}ALL ACCEPTANCE TESTS PASSED!${NC}"
    echo -e "✅ Milestone TEST implementation is complete and compliant"
    exit 0
else
    echo -e "\n❌ ${RED}SOME TESTS FAILED${NC}"
    echo -e "Please fix the failing tests before marking milestone complete"
    exit 1
fi
