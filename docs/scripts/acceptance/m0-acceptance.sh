#!/usr/bin/env bash
set -euo pipefail

echo "🔧 Running M0 Acceptance Tests..."

# Test 1: Clean install and build
echo "1️⃣ Testing clean install and build..."
pnpm install
pnpm build
echo "✅ Build successful"

# Test 2: Lint and type check
echo "2️⃣ Testing lint and type check..."
pnpm lint
pnpm type-check
echo "✅ Lint and type check passed"

# Test 3: Run tests
echo "3️⃣ Testing test suite..."
pnpm exec turbo run test --parallel
echo "✅ All tests passed"

# Test 4: Docker health check
echo "4️⃣ Testing Docker compose stack..."
docker compose down -v >/dev/null 2>&1 || true
docker compose up -d --wait

# Test the health endpoint via Docker
if curl -fs http://localhost:3000/health | grep '"status":"ok"' >/dev/null; then
    echo "✅ Docker API health endpoint working"
else
    echo "❌ Docker API health endpoint failed"
    docker compose down -v >/dev/null 2>&1 || true
    exit 1
fi

# Clean up Docker
docker compose down -v >/dev/null 2>&1 || true

echo "🎉 All M0 acceptance tests passed!"
echo "✅ Repository skeleton is ready for development"
