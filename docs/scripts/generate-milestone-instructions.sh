#!/usr/bin/env bash
set -euo pipefail

# Generate Milestone Instructions Script
# Creates comprehensive, agent-specific instructions for milestone execution

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Usage function
usage() {
    echo "Usage: $0 <milestone-file> --agent=<agent-type>"
    echo ""
    echo "Arguments:"
    echo "  milestone-file    Path to milestone MDX file (e.g., milestone-M0.1.mdx)"
    echo "  --agent          Agent type (cursor, copilot, augment, claude)"
    echo ""
    echo "Examples:"
    echo "  $0 docs/tech-specs/milestones/milestone-M0.1.mdx --agent=cursor"
    echo "  $0 milestone-M0.1.mdx --agent=copilot"
    echo ""
    echo "Output:"
    echo "  Generated instructions will be saved to work-log/<milestone-id>/instructions-for-<agent>.md"
    exit 1
}

# Parse arguments
MILESTONE_FILE=""
AGENT_TYPE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --agent=*)
            AGENT_TYPE="${1#*=}"
            shift
            ;;
        --agent)
            AGENT_TYPE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            if [[ -z "$MILESTONE_FILE" ]]; then
                MILESTONE_FILE="$1"
            else
                log_error "Unknown argument: $1"
                usage
            fi
            shift
            ;;
    esac
done

# Validate arguments
if [[ -z "$MILESTONE_FILE" ]]; then
    log_error "Milestone file is required"
    usage
fi

if [[ -z "$AGENT_TYPE" ]]; then
    log_error "Agent type is required (use --agent=<type>)"
    usage
fi

if [[ ! -f "$MILESTONE_FILE" ]]; then
    log_error "Milestone file not found: $MILESTONE_FILE"
    exit 1
fi

# Validate agent type
SUPPORTED_AGENTS=("cursor" "copilot" "augment" "claude")
if [[ ! " ${SUPPORTED_AGENTS[@]} " =~ " ${AGENT_TYPE} " ]]; then
    log_error "Unsupported agent type: $AGENT_TYPE"
    log_info "Supported agents: ${SUPPORTED_AGENTS[*]}"
    exit 1
fi

# Check if Node.js is available for the instruction generator
if ! command -v node &> /dev/null; then
    log_error "Node.js is required but not installed"
    exit 1
fi

# Check if instruction generator exists
GENERATOR_SCRIPT="docs/scripts/instruction-generator.mjs"
if [[ ! -f "$GENERATOR_SCRIPT" ]]; then
    log_error "Instruction generator not found: $GENERATOR_SCRIPT"
    log_info "Please ensure the instruction generator is properly installed"
    exit 1
fi

# Extract milestone ID from filename
MILESTONE_ID=$(basename "$MILESTONE_FILE" .mdx)
log_info "Generating instructions for milestone: $MILESTONE_ID"
log_info "Target agent: $AGENT_TYPE"

# Create work-log directory if it doesn't exist
WORK_LOG_DIR="work-log/$MILESTONE_ID"
mkdir -p "$WORK_LOG_DIR"

# Generate instructions using Node.js script
log_info "Running instruction generator..."
OUTPUT_FILE="$WORK_LOG_DIR/instructions-for-$AGENT_TYPE.md"

if node "$GENERATOR_SCRIPT" "$MILESTONE_FILE" "$AGENT_TYPE" "$OUTPUT_FILE"; then
    log_success "Instructions generated successfully!"
    log_info "Output file: $OUTPUT_FILE"
    
    # Display file size and line count
    if [[ -f "$OUTPUT_FILE" ]]; then
        LINES=$(wc -l < "$OUTPUT_FILE")
        SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
        log_info "Generated $LINES lines ($SIZE)"
    fi
    
    echo ""
    log_info "Next steps:"
    echo "  1. Review the generated instructions: $OUTPUT_FILE"
    echo "  2. Give the instructions to your $AGENT_TYPE agent"
    echo "  3. Monitor execution and document results"
    echo "  4. Report back on success/failure patterns"
    
else
    log_error "Failed to generate instructions"
    exit 1
fi
