#!/usr/bin/env node
/**
 * Generate a new milestone specification from template
 * Usage: node generate-milestone.mjs M1 "Static Graph Builder"
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Parse command line arguments
const [, , milestoneId, milestoneTitle] = process.argv;

if (!milestoneId || !milestoneTitle) {
  console.error('Usage: node generate-milestone.mjs <ID> "<Title>"');
  console.error(
    'Example: node generate-milestone.mjs M1 "Static Graph Builder"'
  );
  process.exit(1);
}

// Validate milestone ID format
if (!/^M\d+$/.test(milestoneId)) {
  console.error("Error: Milestone ID must be in format M1, M2, etc.");
  process.exit(1);
}

// Paths
const templatePath = path.join(
  __dirname,
  "../tech-specs/process/templates/milestone-template.mdx"
);
const outputPath = path.join(
  __dirname,
  `../tech-specs/milestones/milestone-${milestoneId}.mdx`
);

// Check if template exists
if (!fs.existsSync(templatePath)) {
  console.error(`Error: Template not found at ${templatePath}`);
  process.exit(1);
}

// Check if milestone already exists
if (fs.existsSync(outputPath)) {
  console.error(
    `Error: Milestone ${milestoneId} already exists at ${outputPath}`
  );
  process.exit(1);
}

// Read template
let template = fs.readFileSync(templatePath, "utf8");

// Get current date
const currentDate = new Date().toISOString().split("T")[0];

// Replace placeholders
const replacements = {
  "<ID>": milestoneId,
  "<One-line scope>": milestoneTitle,
  "<Short paragraph of intent>": `Detailed specification for ${milestoneTitle} milestone.`,
  "<YYYY-MM-DD>": currentDate,
  "0.0.0": "0.1.0",
  Draft: "Draft",
  "[]": "[nitishMehrotra]" // Default author, can be changed
};

// Apply replacements
for (const [placeholder, replacement] of Object.entries(replacements)) {
  template = template.replaceAll(placeholder, replacement);
}

// Write new milestone file
fs.writeFileSync(outputPath, template);

console.log(`✅ Created milestone specification: ${outputPath}`);
console.log("");
console.log("Next steps:");
console.log("1. Edit the generated file to add specific requirements");
console.log(
  "2. Run spec-lint to validate: node scripts/spec-lint.mjs " + outputPath
);
console.log("3. Update the milestones/log.mdx index with this milestone");
console.log('4. Update status to "Approved" when ready');
console.log("");
console.log("Add to milestones/log.mdx:");
console.log(
  `| ${milestoneId} | ${milestoneTitle} | 📝 Draft | TBD | 0% | TBD | [\`milestone-${milestoneId}.mdx\`](./milestones/milestone-${milestoneId}.mdx) |`
);

// Optional: Open in default editor (uncomment if desired)
// import { exec } from 'child_process';
// exec(`code "${outputPath}"`, (error) => {
//   if (error) {
//     console.log('Note: Could not open in VS Code automatically');
//   }
// });
